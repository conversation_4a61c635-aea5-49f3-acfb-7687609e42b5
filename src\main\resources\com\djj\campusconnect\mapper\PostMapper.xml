<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.djj.campusconnect.mapper.PostMapper">
    <!-- 修改动态帖子 -->
    <update id="updatePost" parameterType="com.djj.campusconnect.pojo.Post">
        UPDATE post
        <set>
            <if test="content != null">content = #{content},</if>
            <if test="visibility != null">visibility = #{visibility},</if>
            <if test="likeNum != null">like_num = #{likeNum},</if>
            <if test="commentNum != null">comment_num = #{commentNum},</if>
            <if test="hotScore != null">hot_score = #{hotScore},</if>
            updated_at = NOW()
        </set>
        WHERE post_id = #{postId} AND user_id = #{userId}
    </update>
</mapper>
