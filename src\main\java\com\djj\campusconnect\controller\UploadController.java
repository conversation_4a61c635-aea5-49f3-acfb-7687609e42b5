package com.djj.campusconnect.controller;

import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.utils.COSUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
public class UploadController {

    @Autowired
    private COSUtils cosUtils;

    /**
     * 文件上传
     * @param file
     * @return imageUrl
     */
     @PostMapping("/upload")
     public Result<String> upload(MultipartFile file) {
         try {
             String imageUrl = cosUtils.uploadImage(file);
             return Result.success(imageUrl);
         } catch (IOException e) {
             return Result.error(400, "文件上传失败");
         }
     }
}
