# 校园社交平台评分系统设计

本文档汇总了校园社交平台中两个关键评分系统的设计方案：动态热度分数计算和用户兴趣标签分数计算。这些评分系统是实现内容推荐和个性化体验的核心组成部分。

## 1. 动态热度分数计算方法

### 1.1 基础热度计算公式

```
HotScore = (w₁ * log(L + 1) + w₂ * log(C + 1) + w₃ * log(V + 1)) * e^(-(t - t₀)/τ)
```

其中：
- `L` 是动态获得的点赞数
- `C` 是动态获得的评论数
- `V` 是动态的浏览量
- `w₁`、`w₂`、`w₃` 分别是点赞、评论、浏览的权重系数
- `t` 是当前时间（以小时为单位）
- `t₀` 是动态发布时间（以小时为单位）
- `τ` 是时间衰减的半衰期（以小时为单位）

### 1.2 原理解析

#### 1.2.1 用户交互部分

公式中对交互数据（点赞、评论、浏览）采用对数函数 `log(x+1)` 处理有几个关键原因：

- **减少极值影响**：对数函数可以压缩数值范围，使得热门动态和普通动态之间的分数差距不会过大，避免少数超热门内容长期霸占推荐位
- **边际效用递减**：从0到10个点赞的影响远大于从990到1000个点赞的影响
- **防止零值问题**：通过 `log(x+1)` 而非 `log(x)` 避免了当某项指标为0时的计算问题

不同交互行为赋予不同权重，一般来说：`w₂ > w₁ > w₃`，因为评论代表的用户参与度高于点赞，而点赞高于纯浏览。

#### 1.2.2 时间衰减部分

公式使用指数衰减函数 `e^(-(t - t₀)/τ)` 对热度进行时间加权：

- 随着时间推移，内容热度自然衰减
- `τ` 是半衰期参数，控制衰减速率，通常设置在24小时左右
- 当 `t - t₀ = τ` 时，热度降为初始值的约36.8%

### 1.3 参数建议

根据校园社交平台的特点，建议参数设置如下：

- 点赞权重 `w₁` = 1.5
- 评论权重 `w₂` = 2.0
- 浏览权重 `w₃` = 0.5
- 半衰期 `τ` = 24小时（可根据平台活跃度调整）

### 1.4 归一化热度计算（0-100范围）

为使热度分数更加直观，我们将其归一化到0-100的范围：

```
NormalizedHotScore = min(100, max(0, scale * rawHotScore))
```

其中：
- `rawHotScore` 是原始热度分数
- `scale` 是缩放因子，用于将分数映射到0-100范围内

#### 1.4.1 确定缩放因子

**方法1：基于经验值设定**

根据平台历史数据分析，确定一个合理的最大原始热度值 `maxExpectedRawScore`，然后：

```
scale = 100 / maxExpectedRawScore
```

例如，如果通过分析确定平台上99%的内容原始热度值不超过20，则可以设置 `scale = 100/20 = 5`。

**方法2：动态调整缩放因子**

这种方法更加灵活，随着平台数据变化自动调整：

1. 定期（如每天）计算当前所有活跃内容的原始热度值
2. 找出这些值的95或99百分位数作为 `percentileScore`
3. 设置 `scale = 100 / percentileScore`

### 1.5 代码实现示例

```java
public double calculateNormalizedHotScore(Post post) {
    // 权重系数
    final double LIKE_WEIGHT = 1.5;
    final double COMMENT_WEIGHT = 2.0;
    final double VIEW_WEIGHT = 0.5;
    final double HALF_LIFE = 24.0; // 半衰期(小时)
    
    // 缩放因子(可动态调整)
    final double SCALE_FACTOR = 5.0; 
    
    // 计算原始热度分数
    double likeScore = Math.log(post.getLikeCount() + 1) * LIKE_WEIGHT;
    double commentScore = Math.log(post.getCommentCount() + 1) * COMMENT_WEIGHT;
    double viewScore = Math.log(post.getViewCount() + 1) * VIEW_WEIGHT;
    
    long currentTime = System.currentTimeMillis();
    long postTime = post.getCreatedAt().getTime();
    double hoursElapsed = (currentTime - postTime) / (1000.0 * 3600);
    double timeDecay = Math.exp(-hoursElapsed / HALF_LIFE);
    
    double rawHotScore = (likeScore + commentScore + viewScore) * timeDecay;
    
    // 归一化到0-100
    return Math.min(100, Math.max(0, rawHotScore * SCALE_FACTOR));
}
```

#### 1.5.1 动态缩放因子实现

```java
@Service
public class HotScoreService {
    
    @Autowired
    private PostRepository postRepository;
    
    // 初始缩放因子
    private double scaleFactor = 5.0;
    
    // 每天更新一次缩放因子
    @Scheduled(cron = "0 0 0 * * ?")  // 每天午夜执行
    public void updateScaleFactor() {
        // 获取所有活跃内容(例如最近7天内有交互的内容)
        List<Post> activePosts = postRepository.findActivePosts();
        
        if (activePosts.isEmpty()) {
            return;
        }
        
        // 计算所有活跃内容的原始热度分数
        List<Double> rawScores = activePosts.stream()
            .map(this::calculateRawHotScore)
            .sorted()
            .collect(Collectors.toList());
        
        // 取99百分位数作为基准
        int index = (int) Math.ceil(rawScores.size() * 0.99) - 1;
        index = Math.max(0, Math.min(index, rawScores.size() - 1));
        double percentileScore = rawScores.get(index);
        
        // 更新缩放因子，保证99%的内容分数不超过100
        if (percentileScore > 0) {
            this.scaleFactor = 100.0 / percentileScore;
            log.info("Hot score scale factor updated to: {}", this.scaleFactor);
        }
    }
    
    // 计算原始热度分数(不含归一化)
    private double calculateRawHotScore(Post post) {
        final double LIKE_WEIGHT = 1.5;
        final double COMMENT_WEIGHT = 2.0;
        final double VIEW_WEIGHT = 0.5;
        final double HALF_LIFE = 24.0;
        
        double likeScore = Math.log(post.getLikeCount() + 1) * LIKE_WEIGHT;
        double commentScore = Math.log(post.getCommentCount() + 1) * COMMENT_WEIGHT;
        double viewScore = Math.log(post.getViewCount() + 1) * VIEW_WEIGHT;
        
        long currentTime = System.currentTimeMillis();
        long postTime = post.getCreatedAt().getTime();
        double hoursElapsed = (currentTime - postTime) / (1000.0 * 3600);
        double timeDecay = Math.exp(-hoursElapsed / HALF_LIFE);
        
        return (likeScore + commentScore + viewScore) * timeDecay;
    }
    
    // 计算归一化热度分数
    public double calculateNormalizedHotScore(Post post) {
        double rawScore = calculateRawHotScore(post);
        return Math.min(100, Math.max(0, rawScore * this.scaleFactor));
    }
}
```

## 2. 用户兴趣标签分数计算模型

### 2.1 修订版：实用的兴趣标签分数计算公式

```
TagInterestScore = BaseScore + 
                   α * ImplicitInterestScore + 
                   β * ExplicitInterestScore - 
                   γ * NegativeScore
```

各组成部分详解如下：

#### 2.1.1 基础分数 (BaseScore)

新标签的初始分数，通常设为一个正数（例如10）。

#### 2.1.2 隐式兴趣分数 (ImplicitInterestScore)

不同于原来试图追踪每个标签的各种交互计数（这在实际应用中很难实现），我们采用事件驱动式的记录方式：

```
ImplicitInterestScore = 累计交互得分
```

每当用户与带有特定标签的内容发生交互时，系统根据交互类型增加相应的分数：

- 浏览内容：+1分
- 点赞内容：+2分
- 评论内容：+3分
- 分享内容：+4分
- 在内容上停留超过30秒：额外+1分

这种方式无需存储和维护复杂的交互计数统计，只需记录分数增量和最后交互时间。

#### 2.1.3 显式兴趣分数 (ExplicitInterestScore)

```
ExplicitInterestScore = 10 * FollowAction + 5 * ManualSelect - 15 * UnfollowAction
```

其中：
- `FollowAction`：用户是否关注了该标签（布尔值：1或0）
- `ManualSelect`：用户是否在兴趣设置中主动选择了该标签（布尔值：1或0）
- `UnfollowAction`：用户是否取消关注了该标签（布尔值：1或0）

这部分实现相对简单，因为它基于用户的直接行为，可以准确记录。

#### 2.1.4 负面反馈分数 (NegativeScore)

同样采用事件驱动式的记录方式，当用户对带有某标签的内容发出负面反馈时，直接减分：

```
NegativeScore = 累计负面反馈得分
```

- 标记"不感兴趣"：-5分
- 隐藏内容：-10分
- 举报内容：-20分

这样设计可以更直接地反映用户的反感反应，并使系统能快速调整推荐策略。

#### 2.1.5 时间衰减机制

不再单独计算时间衰减调整项，而是使用定期批处理的方式对所有用户-标签兴趣分数进行衰减：

```java
// 每周执行一次的定时任务
@Scheduled(cron = "0 0 0 * * 0")
public void decayAllInterestScores() {
    // 获取所有用户-标签兴趣记录
    List<UserTagInterest> allInterests = userTagInterestRepository.findAll();
    Date now = new Date();
    
    for (UserTagInterest interest : allInterests) {
        // 计算距离上次交互的天数
        long daysElapsed = ChronoUnit.DAYS.between(
            interest.getLastInteractionTime().toInstant(), 
            now.toInstant());
        
        if (daysElapsed > 7) {  // 超过一周没有交互
            // 每周衰减10%
            double decayFactor = Math.pow(0.9, daysElapsed / 7.0);
            interest.setScore(interest.getScore() * decayFactor);
        }
    }
    
    userTagInterestRepository.saveAll(allInterests);
}
```

这种方式更容易实现，并且效果类似——好长时间没有交互的标签兴趣分数会逐渐下降。

### 2.2 修订版参数设置

- α (隐式兴趣权重) = 1.0 （直接使用事件记录的分数）
- β (显式兴趣权重) = 1.5 （增加显式行为的权重）
- γ (负面反馈权重) = 2.0 （强化负面反馈的影响）

### 2.3 归一化处理

用户兴趣标签分数的归一化非常重要，因为：

1. 方便标签间的直接比较
2. 便于将分数转化为权重用于内容推荐
3. 对前端展示更友好（可以直接用百分比表示）

建议的归一化方法：

```java
/**
 * 将原始兴趣分数归一化到 0-100 范围
 */
public double normalizeInterestScore(double rawScore) {
    // 简单截断方法
    return Math.min(100, Math.max(0, rawScore));
    
    // 或者使用Sigmoid函数实现平滑归一化
    // return 100 * (1 / (1 + Math.exp(-rawScore/20)));
}
```

### 2.4 修订版代码实现示例

```java
@Service
public class UserTagInterestService {
    
    @Autowired
    private UserTagInterestRepository userTagInterestRepository;
    
    // 初始基础分数
    private static final double BASE_SCORE = 10.0;
    
    // 权重系数
    private static final double ALPHA = 1.0;  // 隐式兴趣权重
    private static final double BETA = 1.5;   // 显式兴趣权重
    private static final double GAMMA = 2.0;  // 负面反馈权重
    
    /**
     * 记录用户与标签的交互事件
     */
    public void recordUserTagInteraction(Long userId, Long tagId, InteractionType type) {
        // 查找或创建用户-标签记录
        UserTagInterest interest = getOrCreateUserTagInterest(userId, tagId);
        
        // 根据交互类型增加分数
        switch(type) {
            case VIEW:
                interest.incrementScore(1);
                break;
            case LIKE:
                interest.incrementScore(2);
                break;
            case COMMENT:
                interest.incrementScore(3);
                break;
            case SHARE:
                interest.incrementScore(4);
                break;
            case DISINTEREST:
                interest.decrementScore(5);
                break;
            case HIDE:
                interest.decrementScore(10);
                break;
            case REPORT:
                interest.decrementScore(20);
                break;
            case FOLLOW_TAG:
                interest.setFollowed(true);
                interest.setUnfollowed(false);
                break;
            case UNFOLLOW_TAG:
                interest.setFollowed(false);
                interest.setUnfollowed(true);
                break;
            case MANUAL_SELECT:
                interest.setManualSelected(true);
                break;
        }
        
        // 更新最后交互时间
        interest.setLastInteractionTime(new Date());
        
        // 保存更新
        userTagInterestRepository.save(interest);
    }
    
    /**
     * 查找或创建用户-标签兴趣记录
     */
    private UserTagInterest getOrCreateUserTagInterest(Long userId, Long tagId) {
        UserTagInterest interest = userTagInterestRepository.findByUserIdAndTagId(userId, tagId);
        if (interest == null) {
            interest = new UserTagInterest();
            interest.setUserId(userId);
            interest.setTagId(tagId);
            interest.setScore(BASE_SCORE);  // 初始分数
            interest.setCreatedAt(new Date());
        }
        return interest;
    }
    
    /**
     * 计算用户对标签的兴趣分数
     */
    public double calculateUserTagInterestScore(Long userId, Long tagId) {
        UserTagInterest interest = userTagInterestRepository.findByUserIdAndTagId(userId, tagId);
        if (interest == null) {
            return 0.0; // 没有交互记录
        }
        
        // 计算显式兴趣分数
        double explicitScore = 10 * (interest.isFollowed() ? 1 : 0) +
                               5 * (interest.isManualSelected() ? 1 : 0) -
                               15 * (interest.isUnfollowed() ? 1 : 0);
        
        // 计算总分数 (隐式兴趣分数直接使用累计值)
        double totalScore = interest.getBaseScore() +
                           ALPHA * interest.getImplicitScore() +
                           BETA * explicitScore;
        
        // 归一化到0-100范围
        return normalizeInterestScore(totalScore);
    }
    
    /**
     * 归一化兴趣分数
     */
    private double normalizeInterestScore(double rawScore) {
        return Math.min(100, Math.max(0, rawScore));
    }
    
    /**
     * 定期衰减分数
     */
    @Scheduled(cron = "0 0 0 * * 0") // 每周日凌晨执行
    public void decayAllInterestScores() {
        List<UserTagInterest> allInterests = userTagInterestRepository.findAll();
        Date now = new Date();
        
        for (UserTagInterest interest : allInterests) {
            long daysElapsed = ChronoUnit.DAYS.between(
                interest.getLastInteractionTime().toInstant(), 
                now.toInstant());
            
            if (daysElapsed > 7) {  // 超过一周没有交互
                double decayFactor = Math.pow(0.9, daysElapsed / 7.0);
                interest.setScore(interest.getScore() * decayFactor);
            }
        }
        
        userTagInterestRepository.saveAll(allInterests);
    }
}
```

### 2.5 实体类设计

```java
@Entity
@Table(name = "t_user_interest_tag")
@Data
public class UserTagInterest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id")
    private Long userId;
    
    @Column(name = "tag_id")
    private Long tagId;
    
    @Column(name = "score")
    private double score;  // 总的兴趣分数
    
    @Column(name = "implicit_score")
    private double implicitScore;  // 隐式交互分数
    
    @Column(name = "base_score")
    private double baseScore = 10.0;  // 基础分数
    
    @Column(name = "is_followed")
    private boolean followed;  // 是否关注了标签
    
    @Column(name = "is_unfollowed")
    private boolean unfollowed;  // 是否取消关注了标签
    
    @Column(name = "is_manual_selected")
    private boolean manualSelected;  // 是否手动选择了标签
    
    @Column(name = "last_interaction_time")
    private Date lastInteractionTime;  // 最后交互时间
    
    @Column(name = "created_at")
    private Date createdAt;
    
    @Column(name = "updated_at")
    private Date updatedAt;
    
    /**
     * 增加隐式交互分数
     */
    public void incrementScore(double value) {
        this.implicitScore += value;
        this.score += value;
        this.updatedAt = new Date();
    }
    
    /**
     * 减少隐式交互分数
     */
    public void decrementScore(double value) {
        this.implicitScore -= value;
        this.score -= value;
        this.updatedAt = new Date();
    }
}
```

### 2.6 前端交互记录

前端可以发送交互事件到后端，方便记录用户行为：

```javascript
// 发送交互事件到后端
 function recordTagInteraction(tagId, interactionType) {
    fetch('/api/user/tag-interaction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            tagId: tagId,
            type: interactionType
        })
    });
}

// 在不同操作时调用
$('.post-item').on('click', function() {
    const tagIds = $(this).data('tag-ids').split(',');
    // 浏览内容时记录
    tagIds.forEach(tagId => recordTagInteraction(tagId, 'VIEW'));
    
    // 设置计时器记录停留时间
    const startTime = Date.now();
    const checkStayTime = setInterval(() => {
        if (Date.now() - startTime > 30000) { // 30秒
            // 停留超过30秒，增加分数
            tagIds.forEach(tagId => recordTagInteraction(tagId, 'VIEW'));
            clearInterval(checkStayTime);
        }
    }, 30000);
});

// 点赞交互
$('.like-button').on('click', function() {
    const tagIds = $(this).closest('.post-item').data('tag-ids').split(',');
    tagIds.forEach(tagId => recordTagInteraction(tagId, 'LIKE'));
});

// 不感兴趣交互
$('.disinterest-button').on('click', function() {
    const tagIds = $(this).closest('.post-item').data('tag-ids').split(',');
    tagIds.forEach(tagId => recordTagInteraction(tagId, 'DISINTEREST'));
});
```
