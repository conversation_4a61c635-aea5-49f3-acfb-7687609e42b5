package com.djj.campusconnect.mapper;

import com.djj.campusconnect.pojo.Post;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface PostMapper {

    @Select("SELECT * FROM post WHERE post_id = #{postId}")
    public Post getPostById(Integer postId);

    @Select("SELECT * FROM post")
    public List<Post> getAllPost();

    @Insert("INSERT INTO post(user_id, content, visibility, created_at, updated_at)" +
    "VALUES (#{userId}, #{content}, #{visibility}, NOW(), NOW())")
    public Integer addPost(Post post);

    public void updatePost(Post post);

}
