package com.djj.campusconnect.utils;

import com.alibaba.fastjson.JSON;
import com.djj.campusconnect.pojo.Admin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class AdminContext {

    // ThreadLocal用于请求处理中快速访问
    private static final ThreadLocal<Admin> adminThreadLocal = new ThreadLocal<>();

    // Redis键前缀
    private static final String ADMIN_INFO_PREFIX = "admin:info:";
    private static final long EXPIRATION = 30 * 60; // 30分钟

    private static StringRedisTemplate redisTemplate;

    @Autowired
    public void setRedisTemplate(StringRedisTemplate redisTemplate) {
        AdminContext.redisTemplate = redisTemplate;
    }

    /**
     * 用户登录时调用，将管理员信息存入Redis
     */
    public void saveAdminToRedis(Admin admin) {
        if (admin != null) {
            String adminKey = ADMIN_INFO_PREFIX + admin.getAdminId();
            String adminJson = JSON.toJSONString(admin);

            // 存储管理员信息到Redis
            redisTemplate.opsForValue().set(adminKey, adminJson, EXPIRATION, TimeUnit.SECONDS);
        }
    }

    /**
     * 拦截器中调用，从Redis获取管理员信息并设置到ThreadLocal
     */
    public void setCurrentAdmin(Integer adminId) {
        if (adminId != null) {
            // 从Redis获取用户信息
            String adminKey = ADMIN_INFO_PREFIX + adminId;
            String adminJson = redisTemplate.opsForValue().get(adminKey);

            if (adminJson != null) {
                // 刷新Redis过期时间
                redisTemplate.expire(adminKey, EXPIRATION, TimeUnit.SECONDS);
                // 设置到ThreadLocal
                Admin admin = JSON.parseObject(adminJson, Admin.class);
                adminThreadLocal.set(admin);
            }
        }
    }

    /**
     * 在请求处理过程中快速获取当前管理员
     */
    public Admin getCurrentAdmin() {
        return adminThreadLocal.get();
    }

    /**
     * 获取当前管理员ID
     */
    public Integer getCurrentAdminId() {
        Admin admin = getCurrentAdmin();
        return admin != null ? admin.getAdminId() : null;
    }

    /**
     * 请求结束时清理ThreadLocal
     */
    public void remove() {
        adminThreadLocal.remove();
    }

    /**
     * 用户登出时调用，清理Redis和ThreadLocal
     */
    public void logout(Integer adminId) {
        remove();

        if (adminId != null) {
            String adminKey = ADMIN_INFO_PREFIX + adminId;
            redisTemplate.delete(adminKey);
        }
    }

    /**
     * 管理员吗信息更新时调用，同步更新Redis缓存
     */
    public void updateAdminInRedis(Admin admin) {
        if (admin != null) {
            saveAdminToRedis(admin);
        }
    }
}
