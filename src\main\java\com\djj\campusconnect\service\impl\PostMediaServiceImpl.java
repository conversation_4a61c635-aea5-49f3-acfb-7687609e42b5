package com.djj.campusconnect.service.impl;

import com.djj.campusconnect.mapper.PostMapper;
import com.djj.campusconnect.mapper.PostMediaMapper;
import com.djj.campusconnect.pojo.PostMedia;
import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.service.PostMediaService;
import com.djj.campusconnect.utils.COSUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Service
public class PostMediaServiceImpl implements PostMediaService {

    @Autowired
    PostMediaMapper postMediaMapper;

    @Autowired
    COSUtils cosUtils;

    @Override
    public String upload(MultipartFile file, Integer postId, Integer mediaType, Integer sortOrder) {
        try {
            String mediaUrl = "";
            if (mediaType == 0) {
                mediaUrl = cosUtils.uploadImage(file);
            } else if (mediaType == 1) {
                mediaUrl = cosUtils.uploadVideo(file);
            } else {
                return null;
            }
            PostMedia postMedia = new PostMedia();
            postMedia.setPostId(postId);
            postMedia.setSortOrder(sortOrder);
            postMedia.setMediaType(mediaType);
            postMedia.setMediaUrl(mediaUrl);
            postMediaMapper.insert(postMedia);
            return mediaUrl;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public List<PostMedia> getPostMediaByPostId(Integer postId) {
        return postMediaMapper.selectByPostId(postId);
    }

}
