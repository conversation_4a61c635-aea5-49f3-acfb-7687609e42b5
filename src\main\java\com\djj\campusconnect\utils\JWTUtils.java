package com.djj.campusconnect.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class JWTUtils {

    private static String secretKey = "campus_connect";// 密钥
    private static Long expireTime = 432000000L; // 过期时间，单位秒

    /**
     * 生成JWT
     *
     * @param claims 自定义声明
     * @return JWT字符串
     */
    public static String generateJWT(Map<String, Object> claims) {
         String jwt = Jwts.builder()
                 .setClaims(claims)
                 .setExpiration(new Date(System.currentTimeMillis() + expireTime))
                 .signWith(SignatureAlgorithm.HS256, secretKey)
                 .compact();
         return jwt;
    }
    
    /**
     * 根据用户ID生成包含用户信息的JWT
     *
     * @param userId 用户ID
     * @return JWT字符串
     */
    public static String generateUserJWT(Integer userId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        return generateJWT(claims);
    }

    /**
     * 解析JWT
     *
     * @param jwt JWT字符串
     * @return Claims对象
     */
    public static Claims parseJWT(String jwt) {
        Claims claims = Jwts.parser()
                .setSigningKey(secretKey)
                .parseClaimsJws(jwt)
                .getBody();
        return claims;
    }
}
