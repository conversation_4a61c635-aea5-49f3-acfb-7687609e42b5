package com.djj.campusconnect.controller;

import com.alibaba.fastjson.JSON;
import com.djj.campusconnect.pojo.PasswordResetRequest;
import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.pojo.User;
import com.djj.campusconnect.pojo.VerifyCodeRequest;
import com.djj.campusconnect.service.UserService;
import com.djj.campusconnect.utils.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserContext userContext;

    /*
     * 处理登录请求
     * @param user 用户信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result login(@RequestBody User user) {
        String message = userService.login(user.getEmail(), user.getPasswordHash());
        if (message.equals("用户不存在")) {
            return Result.error(400, "用户不存在");
        } else if (message.equals("密码错误")) {
            return Result.error(400, "密码错误");
        } else if (message.equals("账号已冻结")) {
            return Result.error(403, "账号已冻结");
        }
        User currentUser = userContext.getCurrentUser();
        Map<String, Object> data = new HashMap<>();
        data.put("token", message);
        data.put("user", currentUser);
        return Result.success(data);
    }

    /*
     * 处理注册请求
     * @param user 用户信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result register(@RequestBody Map<String, Object> requestMap) {
        try {
            // 使用FastJSON将Map转换为User对象
            User user = JSON.parseObject(JSON.toJSONString(requestMap.get("user")), User.class);

            // 使用FastJSON将Map转换为VerifyCodeRequest对象
            VerifyCodeRequest verifyCodeRequest = JSON.parseObject(
                    JSON.toJSONString(requestMap.get("request")),
                    VerifyCodeRequest.class
            );

            Integer result = userService.register(user, verifyCodeRequest);

            if (result == 1) {
                return Result.success("注册成功");
            } else if (result == 0) {
                return Result.error(400, "注册失败");
            } else {
                return Result.error(400, "验证码错误或已过期");
            }
        } catch (Exception e) {
            log.error("注册失败", e);
            return Result.error(400, "请求格式错误: " + e.getMessage());
        }
    }

    /*
     * 处理用户信息更新请求
     * @param user 用户信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public Result updateUser(@RequestBody User user) {
        Boolean result = userService.updateUser(user);
        if (result) {
            return Result.success("更新成功");
        } else {
            return Result.error(400, "更新失败");
        }
    }

    /*
     * 处理获取用户信息请求
     */
    @GetMapping("/profile")
    public Result getUserProfile() {
        User user = userContext.getCurrentUser();
        if (user != null) {
            return Result.success(user);
        } else {
            return Result.error(400, "用户不存在");
        }
    }


    @PostMapping("/sendCode")
    public Result requestPasswordReset(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        if (email == null || email.isEmpty()) {
            log.info("注册邮箱:" + email);
            return Result.error(400, "邮箱不能为空");
        }
        
        log.info("开始发送验证码到邮箱: {}" , email);
        try {
            Boolean result = userService.sendCode(email);
            if (result) {
                return Result.success("验证码已发送到您的邮箱");
            } else {
                return Result.error(400, "邮箱不存在或发送失败");
            }
        } catch (Exception e) {
            log.error("发送验证码失败", e);
            return Result.error(500, "发送验证码失败: " + e.getMessage());
        }
    }

}
