# Campus Connect 项目实现文档

## 项目结构

项目采用分层架构设计，主要包含以下几个层次：

```
campus-connect/
├── config/             # 配置类
│   └── WebMvcConfig.java  # Spring MVC配置，包括拦截器注册
├── controller/         # 控制器层，处理HTTP请求
│   ├── PasswordResetController.java  # 密码重置相关接口
│   ├── UploadController.java  # 文件上传相关接口
│   └── UserController.java    # 用户相关接口
├── pojo/               # 数据模型
│   ├── PasswordResetRequest.java  # 密码重置请求
│   ├── ResetPasswordRequest.java  # 重置密码请求
│   ├── Result.java    # 统一响应结果封装
│   ├── User.java      # 用户模型
│   └── VerifyCodeRequest.java  # 验证码验证请求
├── mapper/             # DAO层，数据库访问
│   └── UserMapper.java  # 用户数据访问
├── service/            # 业务逻辑层
│   ├── PasswordResetService.java  # 密码重置服务接口
│   ├── UserService.java     # 用户服务接口
│   └── impl/                # 服务实现类
│       ├── PasswordResetServiceImpl.java  # 密码重置服务实现
│       └── UserServiceImpl.java  # 用户服务实现
├── utils/              # 工具类
│   ├── AdminContext.java         # 管理员上下文工具(ThreadLocal)
│   ├── COSUtils.java             # 云存储工具
│   ├── EmailUtils.java           # 邮件发送工具
│   ├── JWTUtils.java             # JWT工具类
│   ├── LoginCheckInterceptor.java  # 登录检查拦截器
│   ├── OCRUtils.java             # OCR识别工具
│   └── UserContext.java          # 用户上下文工具(ThreadLocal)
└── CampusConnectApplication.java  # 应用程序入口
```

## 用户身份识别实现详情

项目采用了拦截器+ThreadLocal方案来实现用户身份识别，该方案简单高效，适合中小型应用。主要组件如下：

### 1. 用户上下文 (UserContext)

使用ThreadLocal存储当前请求的用户信息，确保线程安全。

```java
public class UserContext {
    // 使用ThreadLocal存储当前用户信息
    private static final ThreadLocal<User> userThreadLocal = new ThreadLocal<>();
    
    // 设置当前用户信息
    public static void setCurrentUser(User user) {
        userThreadLocal.set(user);
    }
    
    // 获取当前用户信息
    public static User getCurrentUser() {
        return userThreadLocal.get();
    }
    
    // 获取当前用户ID
    public static Integer getCurrentUserId() {
        User user = getCurrentUser();
        return user != null ? user.getUserId() : null;
    }
    
    // 清除当前用户信息，防止内存泄漏
    public static void remove() {
        userThreadLocal.remove();
    }
}
```

### 2. JWT工具类 (JWTUtils)

用于生成和解析JWT令牌，令牌中包含用户ID等关键信息。

```java
public class JWTUtils {
    private static String secretKey = "campus_connect";// 密钥
    private static Long expireTime = 432000000L; // 过期时间，5天
    
    // 生成JWT令牌
    public static String generateJWT(Map<String, Object> claims) {...}
    
    // 根据用户ID生成JWT
    public static String generateUserJWT(Integer userId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        return generateJWT(claims);
    }
    
    // 解析JWT令牌
    public static Claims parseJWT(String jwt) {...}
}
```

### 3. 登录检查拦截器 (LoginCheckInterceptor)

拦截HTTP请求，验证JWT令牌，并将用户信息存入ThreadLocal。

```java
public class LoginCheckInterceptor implements HandlerInterceptor {
    @Autowired
    private JWTUtils jwtUtils;
    
    @Autowired
    private UserService userService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取并验证token
        // 解析token获取用户ID
        // 查询用户信息并存入ThreadLocal
    }
    
    @Override
    public void afterCompletion(...) {
        // 请求结束后清理ThreadLocal
        UserContext.remove();
    }
}
```

### 4. 拦截器配置 (WebMvcConfig)

配置拦截器的拦截规则，排除登录注册等不需要验证的路径。

```java
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Autowired
    private LoginCheckInterceptor loginCheckInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(loginCheckInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                    "/user/login", 
                    "/user/register",
                    // 其他不需要验证的路径 
                );
    }
}
```

### 5. 用户控制器 (UserController)

处理用户登录注册等操作，并在登录成功时生成包含用户ID的JWT令牌。

```java
@RestController("/user")
public class UserController {
    @Autowired
    private UserService userService;
    
    @PostMapping("/login")
    public Result<String> login(@RequestBody User user) {
        // 验证用户名密码
        // 生成JWT令牌
        String token = JWTUtils.generateUserJWT(user.getUserId());
        return Result.success(token);
    }
    
    @GetMapping("/getCurrentUser")
    public Result<User> getCurrentUser() {
        // 从ThreadLocal获取当前用户
        User user = UserContext.getCurrentUser();
        // 返回用户信息
    }
    
    // 其他用户相关接口
}
```

## 数据流程

1. 用户登录：用户提交用户名密码 → 验证成功后生成包含用户ID的JWT令牌 → 返回给前端
2. 后续请求：前端请求携带JWT令牌 → 拦截器验证令牌 → 解析获取用户ID → 查询用户信息 → 存入ThreadLocal → 处理业务逻辑
3. 获取当前用户：通过`UserContext.getCurrentUser()`方法从ThreadLocal获取当前用户信息
4. 请求结束：拦截器的afterCompletion方法清理ThreadLocal，防止内存泄漏

## 优势和局限性

### 优势
- 实现简单，容易理解
- 无需每个方法都传递用户信息
- 线程安全，每个请求独立
- 内存占用小，无需额外的会话存储

### 局限性
- 不支持复杂的权限控制
- 没有内置的防止CSRF、XSS等安全机制
- 无法跨请求持久化用户状态(每个请求都需要重新验证和加载用户)

## 密码重置功能实现

项目实现了基于邮件验证码的密码重置功能，使用户可以在忘记密码时通过邮箱验证重新设置密码。主要组件如下：

### 1. 邮件工具类 (EmailUtils)

负责生成、发送和验证邮箱验证码，使用内存缓存存储验证码和过期时间。

```java
@Component
public class EmailUtils {
    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    // 存储验证码和过期时间
    private final Map<String, VerificationCode> verificationCodes = new HashMap<>();
    
    // 验证码有效期（5分钟）
    private static final long VERIFICATION_CODE_EXPIRY = 5 * 60 * 1000;
    
    // 生成并发送验证码
    public String sendVerificationCode(String email) {
        // 生成随机验证码
        // 发送邮件
        // 存储验证码和过期时间
    }
    
    // 验证验证码
    public boolean verifyCode(String email, String code) {
        // 检查验证码是否存在
        // 检查验证码是否过期
        // 检查验证码是否匹配
    }
    
    // 内部类，存储验证码和过期时间
    private static class VerificationCode {
        private final String code;
        private final long expiryTime;
    }
}
```

### 2. 密码重置服务 (PasswordResetService)

提供密码重置相关的业务逻辑，包括发送验证码、验证验证码和重置密码。

```java
public interface PasswordResetService {
    // 发送密码重置验证码
    Boolean sendPasswordResetCode(String email);
    
    // 验证邮箱验证码
    Boolean verifyEmailCode(VerifyCodeRequest request);
    
    // 重置密码
    Boolean resetPassword(ResetPasswordRequest request);
}
```

### 3. 密码重置控制器 (PasswordResetController)

提供密码重置相关的HTTP接口，包括请求重置密码、验证验证码和重置密码。

```java
@RestController
@RequestMapping("/password")
public class PasswordResetController {
    @Autowired
    private PasswordResetService passwordResetService;
    
    // 请求发送密码重置验证码
    @PostMapping("/forgot")
    public Result requestPasswordReset(@RequestBody PasswordResetRequest request) {...}
    
    // 验证邮箱验证码
    @PostMapping("/verify")
    public Result verifyCode(@RequestBody VerifyCodeRequest request) {...}
    
    // 重置密码
    @PostMapping("/reset")
    public Result resetPassword(@RequestBody ResetPasswordRequest request) {...}
}
```

### 4. 密码重置数据模型

- **PasswordResetRequest**: 包含用户邮箱，用于请求发送验证码
- **VerifyCodeRequest**: 包含用户邮箱和验证码，用于验证验证码
- **ResetPasswordRequest**: 包含用户邮箱、验证码和新密码，用于重置密码

### 5. 邮件服务配置

在`application.properties`中配置邮件服务器信息：

```properties
# 邮件服务器配置
spring.mail.host=smtp.qq.com
spring.mail.port=465
spring.mail.username=<EMAIL>
spring.mail.password=your-authorization-code
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.ssl.enable=true
```

## 数据流程 - 密码重置

1. 用户请求密码重置：提交邮箱 → 系统生成验证码 → 发送验证码邮件 → 存储验证码及过期时间
2. 用户验证验证码：提交邮箱和验证码 → 系统验证验证码有效性 → 返回验证结果
3. 用户重置密码：提交邮箱、验证码和新密码 → 系统再次验证验证码 → 更新用户密码 → 返回结果

## 角色权限控制

项目已经实现了基于 ThreadLocal 的简单角色权限控制系统，通过 UserContext 和 AdminContext 分别管理用户和管理员的信息。

### 1. 管理员上下文 (AdminContext)

与 UserContext 类似，用于存储当前请求的管理员信息。

```java
public class AdminContext {
    // 使用ThreadLocal存储当前管理员信息
    private static final ThreadLocal<Admin> adminThreadLocal = new ThreadLocal<>();
    
    // 设置当前管理员信息
    public static void setCurrentAdmin(Integer adminId) {...}
    
    // 获取当前管理员信息
    public static Admin getCurrentAdmin() {...}
    
    // 清除当前管理员信息
    public static void remove() {
        adminThreadLocal.remove();
    }
}
```

### 2. 权限控制实现

系统通过以下方式实现角色权限控制：

1. **用户和管理员独立登录**：用户和管理员分别生成不同的JWT令牌，其中包含各自的ID信息。

2. **拦截器判断角色**：在LoginCheckInterceptor中解析JWT令牌，判断当前请求是用户还是管理员，并将信息存入相应的ThreadLocal中。

3. **接口级别的角色限制**：
   - 管理员端点使用`AdminContext.getCurrentAdmin()`获取管理员信息，如果非管理员访问则返回错误结果。
   - 用户端点使用`UserContext.getCurrentUser()`获取用户信息，如果非用户访问则返回错误结果。

这种设计确保了：
- 用户无法访问管理员专用接口
- 管理员无法以用户身份访问用户专用接口
- 单个请求中只能以单一角色访问系统

## 后续优化方向

1. 实现更精细的权限控制，如基于资源和操作的权限划分
2. 实现令牌刷新机制
3. 增强安全性，如添加IP绑定、设备标识等
4. 实现JWT黑名单机制，支持登出功能
5. 考虑在大型应用场景下迁移到Spring Security
6. 优化密码重置功能，完善管理员重置用户密码功能
7. 实现验证码图片生成，防止邮箱验证码暴力破解
