#### 5.2.4 VO类设计

##### 5.2.4.1 PostVO类

```java
/**
 * 动态显示对象
 */
@Data
public class PostVO {
    
    /**
     * 动态ID
     */
    private Long postId;
    
    /**
     * 发布者ID
     */
    private Long userId;
    
    /**
     * 发布者昵称
     */
    private String nickname;
    
    /**
     * 发布者头像
     */
    private String avatarUrl;
    
    /**
     * 发布者院系/专业
     */
    private String department;
    
    /**
     * 动态内容
     */
    private String content;
    
    /**
     * 动态类型
     */
    private Integer postType;
    
    /**
     * 发布时间
     */
    private Date createdAt;
    
    /**
     * 点赞数
     */
    private Integer likeCount;
    
    /**
     * 评论数
     */
    private Integer commentCount;
    
    /**
     * 浏览量
     */
    private Integer viewCount;
    
    /**
     * 当前用户是否已点赞
     */
    private Boolean isLiked;
    
    /**
     * 媒体列表
     */
    private List<MediaVO> mediaList;
    
    /**
     * 标签列表
     */
    private List<TagVO> tagList;
    
    /**
     * 是否来自好友
     */
    private Boolean fromFriend;
    
    /**
     * 好友昵称（当fromFriend=true时有值）
     */
    private String friendNickname;
    
    /**
     * 推荐原因（推荐页面使用）
     */
    private String recommendReason;
}
```

##### 5.2.4.2 PostDetailVO类

```java
/**
 * 动态详情对象
 */
@Data
public class PostDetailVO extends PostVO {
    
    /**
     * 评论列表
     */
    private List<CommentVO> commentList;
    
    /**
     * 发布者是否是当前用户的好友
     */
    private Boolean isFriend;
    
    /**
     * 可见性
     */
    private Integer visibility;
    
    /**
     * 分享数
     */
    private Integer shareCount;
}
```

##### 5.2.4.3 MediaVO类

```java
/**
 * 媒体对象
 */
@Data
public class MediaVO {
    
    /**
     * 媒体ID
     */
    private Long mediaId;
    
    /**
     * 媒体URL
     */
    private String mediaUrl;
    
    /**
     * 媒体类型，1=图片，2=视频
     */
    private Integer mediaType;
    
    /**
     * 媒体缩略图URL（当mediaType=2时有值）
     */
    private String thumbnailUrl;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
}
```

##### 5.2.4.4 TagVO类

```java
/**
 * 标签对象
 */
@Data
public class TagVO {
    
    /**
     * 标签ID
     */
    private Long tagId;
    
    /**
     * 标签名称
     */
    private String name;
    
    /**
     * 标签热度
     */
    private Integer hotCount;
}
```

##### 5.2.4.5 CommentVO类

```java
/**
 * 评论对象
 */
@Data
public class CommentVO {
    
    /**
     * 评论ID
     */
    private Long commentId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户昵称
     */
    private String nickname;
    
    /**
     * 用户头像
     */
    private String avatarUrl;
    
    /**
     * 评论内容
     */
    private String content;
    
    /**
     * 点赞数
     */
    private Integer likeCount;
    
    /**
     * 是否已点赞
     */
    private Boolean isLiked;
    
    /**
     * 评论时间
     */
    private Date createdAt;
    
    /**
     * 回复列表
     */
    private List<CommentVO> replyList;
    
    /**
     * 被回复的用户ID
     */
    private Long replyToUserId;
    
    /**
     * 被回复的用户昵称
     */
    private String replyToNickname;
}
```

##### 5.2.4.6 UserInterestTagVO类

```java
/**
 * 用户兴趣标签对象
 */
@Data
public class UserInterestTagVO {
    
    /**
     * 标签ID
     */
    private Long tagId;
    
    /**
     * 标签名称
     */
    private String name;
    
    /**
     * 兴趣分数
     */
    private Integer score;
}
```
