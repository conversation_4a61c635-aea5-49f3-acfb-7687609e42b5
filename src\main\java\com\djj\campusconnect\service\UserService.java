package com.djj.campusconnect.service;

import com.djj.campusconnect.pojo.User;
import com.djj.campusconnect.pojo.VerifyCodeRequest;
import org.springframework.web.bind.annotation.RequestBody;

public interface UserService {

    public String login(String email, String password);

    public Integer register(User user, VerifyCodeRequest request);

    public Boolean updateUser(User user);

    public User getUserById(Integer userId);

    public Boolean sendCode(String email);
}
