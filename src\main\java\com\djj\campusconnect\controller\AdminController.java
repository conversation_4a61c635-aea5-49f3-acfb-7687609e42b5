package com.djj.campusconnect.controller;

import com.djj.campusconnect.pojo.Admin;
import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private AdminService adminService;

    @PostMapping("/login")
    public Result login(@RequestBody Admin admin) {
        String message = adminService.login(admin.getEmail(), admin.getPasswordHash());
        if (message.equals("管理员不存在")) {
            return Result.error(400, message);
        } else if (message.equals("密码错误")) {
            return Result.error(400, message);
        }
        return Result.success(message);
    }
}
