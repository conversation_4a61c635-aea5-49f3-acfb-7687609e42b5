package com.djj.campusconnect.controller;

import com.djj.campusconnect.pojo.Post;
import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.service.PostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/post")
public class PostController {

    @Autowired
    private PostService postService;

    @GetMapping("/all")
    public Result getAllPost() {
        return Result.success(postService.getAllPost());
    }


    @GetMapping("/hot")
    public Result getHotPost(Integer start, Integer PostNum) {
        start = start == null ? 0 : start;
        PostNum = PostNum == null ? 15 : PostNum;
        return Result.success(postService.getHotPost(start, PostNum));
    }

    @GetMapping("/new")
    public Result getNewPost(Integer start, Integer PostNum) {
        start = start == null ? 0 : start;
        PostNum = PostNum == null ? 15 : PostNum;
        return Result.success(postService.getNewPost(start, PostNum));
    }

    @PostMapping("/create")
    public Result createPost(@RequestBody Post post) {
        int result = postService.addPost(post);
        if (result == 1) {
            return Result.success("帖子创建成功");
        } else {
            return Result.error(400, "帖子创建失败");
        }
    }

}
