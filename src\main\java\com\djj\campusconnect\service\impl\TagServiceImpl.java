package com.djj.campusconnect.service.impl;

import com.djj.campusconnect.mapper.TagMapper;
import com.djj.campusconnect.pojo.Tag;
import com.djj.campusconnect.pojo.User;
import com.djj.campusconnect.service.TagService;
import com.djj.campusconnect.utils.AdminContext;
import com.djj.campusconnect.utils.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TagServiceImpl implements TagService {

    @Autowired
    TagMapper tagMapper;

    @Autowired
    UserContext userContext;


    @Override
    public List<Tag> findAllTags() {
        return tagMapper.getAllTags();
    }

    @Override
    public Integer addTag(Tag tag) {
        if (userContext.getCurrentUser() == null) {
            tag.setType(0);
        } else {
            tag.setType(1);
        }
        return tagMapper.insertTag(tag);
    }
}
