<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.djj.campusconnect.mapper.UserMapper">
    <insert id="createUser" parameterType="com.djj.campusconnect.pojo.User">
        INSERT INTO user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="email != null">email,</if>
            <if test="passwordHash != null">password_hash,</if>
            <if test="nickname != null">nickname,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="authStatus != null">auth_status,</if>
            <if test="accountStatus != null">account_status,</if>
            <if test="department != null">department,</if>
            <if test="school != null">school,</if>
            <if test="beforeCardUrl != null">before_card_url,</if>
            <if test="afterCardUrl != null">after_card_url,</if>
            created_at,
            updated_at,
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="email != null">#{email},</if>
            <if test="passwordHash != null">#{passwordHash},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="authStatus != null">#{authStatus},</if>
            <if test="accountStatus != null">#{accountStatus},</if>
            <if test="department != null">#{department},</if>
            <if test="school != null">#{school},</if>
            <if test="beforeCardUrl != null">#{beforeCardUrl},</if>
            <if test="afterCardUrl != null">#{afterCardUrl},</if>
            NOW(),
            NOW(),
        </trim>
    </insert>

    <update id="updateUser" parameterType="com.djj.campusconnect.pojo.User">
        UPDATE user
        <set>
            <if test="email != null">email = #{email},</if>
            <if test="passwordHash != null">password_hash = #{passwordHash},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="accountStatus != null">account_status = #{accountStatus},</if>
            <if test="department != null">department = #{department},</if>
            <if test="school != null">school = #{school},</if>
            <if test="beforeCardUrl != null">before_card_url = #{beforeCardUrl},</if>
            <if test="afterCardUrl != null">after_card_url = #{afterCardUrl},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id}
    </update>
</mapper>