package com.djj.campusconnect.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {
    private Integer userId;
    private String email;
    private String nickname;
    private String passwordHash;
    private String avatarUrl;
    private Integer authStatus;
    private Integer accountStatus;
    private String department;
    private String school;
    private String beforeCardUrl;
    private String afterCardUrl;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
