package com.djj.campusconnect.controller;

import com.djj.campusconnect.pojo.PostMedia;
import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.service.PostMediaService;
import com.djj.campusconnect.service.PostService;
import com.djj.campusconnect.utils.COSUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/post/media")
public class PostMediaController {

    @Autowired
    private PostMediaService postMediaService;

    @Autowired
    private PostService postService;

    /**
     * 动态媒体文件上传
     * @param file
     * @return imageUrl
     */
    @PostMapping("/upload")
    public Result upload(MultipartFile file, Integer postId, Integer mediaType, Integer sortOrder) {
        if (!postService.verifyPostOfCurrentUser(postId)) {
            return Result.error(400, "没有权限");
        }
        String mediaUrl = postMediaService.upload(file, postId, mediaType, sortOrder);
        if (mediaUrl != null) {
            return Result.success(mediaUrl);
        } else {
            return Result.error(400, "上传失败");
        }
    }

    @GetMapping("/getPostMedia")
    public Result getPostMedia(Integer postId) {
        return Result.success(postMediaService.getPostMediaByPostId(postId));
    }

}
