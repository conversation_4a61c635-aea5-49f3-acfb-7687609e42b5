package com.djj.campusconnect.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 图片验证码工具类
 */
@Component
public class CaptchaUtils {

    private static final String CAPTCHA_PREFIX = "captcha:";
    private static final long CAPTCHA_EXPIRATION = 5 * 60; // 5分钟有效期
    
    private static final int WIDTH = 120;
    private static final int HEIGHT = 40;
    private static final int CODE_LENGTH = 4;
    private static final char[] CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".toCharArray();
    
    private final Random random = new Random();
    
    private final StringRedisTemplate redisTemplate;
    
    @Autowired
    public CaptchaUtils(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 生成验证码图片并返回Base64编码
     * 
     * @param captchaId 验证码ID，通常使用UUID或其他唯一标识符
     * @return 验证码图片的Base64编码
     */
    public String generateCaptcha(String captchaId) {
        // 生成随机验证码
        String code = generateRandomCode(CODE_LENGTH);
        
        // 将验证码存入Redis
        redisTemplate.opsForValue().set(CAPTCHA_PREFIX + captchaId, code, CAPTCHA_EXPIRATION, TimeUnit.SECONDS);
        
        // 生成验证码图片
        BufferedImage image = createCaptchaImage(code);
        
        // 将图片转为Base64编码
        return imageToBase64(image);
    }
    
    /**
     * 验证验证码
     * 
     * @param captchaId 验证码ID
     * @param inputCode 用户输入的验证码
     * @return 验证结果
     */
    public boolean verifyCaptcha(String captchaId, String inputCode) {
        if (captchaId == null || inputCode == null) {
            return false;
        }
        
        String key = CAPTCHA_PREFIX + captchaId;
        String storedCode = redisTemplate.opsForValue().get(key);
        
        if (storedCode == null) {
            return false; // 验证码不存在或已过期
        }
        
        // 验证成功后删除验证码，防止重复使用
        redisTemplate.delete(key);
        
        // 不区分大小写比较
        return storedCode.equalsIgnoreCase(inputCode);
    }
    
    /**
     * 生成随机验证码
     */
    private String generateRandomCode(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(CHARS[random.nextInt(CHARS.length)]);
        }
        return sb.toString();
    }
    
    /**
     * 创建验证码图片
     */
    private BufferedImage createCaptchaImage(String code) {
        // 创建图片缓冲区
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 设置背景色
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, WIDTH, HEIGHT);
        
        // 绘制干扰线
        g.setColor(Color.LIGHT_GRAY);
        for (int i = 0; i < 20; i++) {
            int x1 = random.nextInt(WIDTH);
            int y1 = random.nextInt(HEIGHT);
            int x2 = random.nextInt(WIDTH);
            int y2 = random.nextInt(HEIGHT);
            g.drawLine(x1, y1, x2, y2);
        }
        
        // 设置字体
        g.setFont(new Font("Arial", Font.BOLD, 24));
        
        // 绘制验证码
        for (int i = 0; i < code.length(); i++) {
            g.setColor(new Color(random.nextInt(100), random.nextInt(100), random.nextInt(100)));
            // 随机旋转 -30 到 30 度
            double degree = (random.nextInt(60) - 30) * Math.PI / 180;
            g.rotate(degree, WIDTH / 4 * i + 15, HEIGHT / 2);
            g.drawString(String.valueOf(code.charAt(i)), WIDTH / 4 * i + 10, HEIGHT / 2 + 8);
            g.rotate(-degree, WIDTH / 4 * i + 15, HEIGHT / 2);
        }
        
        g.dispose();
        return image;
    }
    
    /**
     * 将图片转换为Base64编码
     */
    private String imageToBase64(BufferedImage image) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ImageIO.write(image, "png", baos);
            byte[] bytes = baos.toByteArray();
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            throw new RuntimeException("Failed to convert image to base64", e);
        }
    }
}
