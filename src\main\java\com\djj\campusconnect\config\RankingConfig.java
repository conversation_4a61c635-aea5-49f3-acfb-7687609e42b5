package com.djj.campusconnect.config;

import com.djj.campusconnect.pojo.Post;
import com.djj.campusconnect.service.PostService;
import com.djj.campusconnect.utils.Rank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.List;

/**
 * 排行榜配置类
 * 处理排行榜的初始化和定时更新
 */
@Slf4j
@Configuration
@EnableScheduling
public class RankingConfig implements ApplicationRunner {

    @Autowired
    private Rank rankUtil;
    
    @Autowired
    private PostService postService;
    
    // 排行榜缓存过期时间（秒）
    private static final long RANK_EXPIRATION = 60 * 60; // 60分钟
    // 提前更新时间（分钟）
    private static final int ADVANCE_UPDATE_MINUTES = 5;
    
    /**
     * 应用启动时初始化排行榜
     */
    @Override
    public void run(ApplicationArguments args) {
        log.info("应用启动，初始化排行榜....");
        initializeRankings();
    }
    
    /**
     * 初始化排行榜数据
     */
    private void initializeRankings() {
        try {
            // 获取所有帖子
            List<Post> allPosts = postService.getAllPost();
            if (allPosts != null && !allPosts.isEmpty()) {
                // 创建热门榜和最新榜，使用同一份数据
                rankUtil.createHotPostsRankingToRedis(allPosts);
                rankUtil.createTimePostsRankingToRedis(allPosts);
                log.info("排行榜初始化完成！共加载帖子: {} 条", allPosts.size());
            } else {
                log.warn("暂无帖子数据，排行榜初始化空表");
            }
        } catch (Exception e) {
            log.error("排行榜初始化失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 定时更新排行榜 - 每55分钟执行一次
     * 在排行榜过期前5分钟更新，避免缓存失效
     */
    @Scheduled(cron = "0 */55 * * * ?")
    public void scheduledRankingUpdate() {
        log.info("定时任务：更新排行榜....");
        try {
            // 获取所有帖子
            List<Post> allPosts = postService.getAllPost();
            if (allPosts != null && !allPosts.isEmpty()) {
                // 更新热门榜和最新榜，使用同一份数据
                rankUtil.updateHotPostsRanking(allPosts);
                rankUtil.updateTimePostsRanking(allPosts);
                log.info("排行榜更新完成！共更新帖子: {} 条", allPosts.size());
            } else {
                log.warn("更新时暂无帖子数据，排行榜保持空表");
            }
        } catch (Exception e) {
            log.error("排行榜更新失败: {}", e.getMessage(), e);
        }
    }
}
