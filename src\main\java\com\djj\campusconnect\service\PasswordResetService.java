package com.djj.campusconnect.service;

import com.djj.campusconnect.pojo.ResetPasswordRequest;
import com.djj.campusconnect.pojo.VerifyCodeRequest;

/**
 * 密码重置服务接口
 */
public interface PasswordResetService {
    
    /**
     * 发送密码重置验证码
     * @param email 用户邮箱
     * @return 成功发送返回true，失败返回false
     */
    Boolean sendPasswordResetCode(String email);
    
    /**
     * 验证邮箱验证码
     * @param request 包含邮箱和验证码的请求
     * @return 验证成功返回true，失败返回false
     */
    Boolean verifyEmailCode(VerifyCodeRequest request);
    
    /**
     * 重置密码
     * @param request 包含邮箱、验证码和新密码的请求
     * @return 重置成功返回true，失败返回false
     */
    Boolean resetPassword(ResetPasswordRequest request);
}
