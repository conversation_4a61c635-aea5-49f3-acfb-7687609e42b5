spring.application.name=campus-connect

spring.datasource.url=******************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=100MB

mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis.configuration.map-underscore-to-camel-case=true

# 腾讯云COS配置
qcloud.cos.bucketName=social-platform-1349546598
qcloud.cos.baseURL=https://social-platform-1349546598.cos.ap-guangzhou.myqcloud.com/

# Redis???????
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6380
spring.data.redis.password=root
spring.data.redis.database=0

# ????????????????????
spring.data.redis.jedis.pool.max-active=8
spring.data.redis.jedis.pool.max-wait=-1ms
spring.data.redis.jedis.pool.min-idle=0
spring.data.redis.jedis.pool.max-idle=8

spring.mail.host=smtp.qq.com
spring.mail.port=465
spring.mail.username=<EMAIL>
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.ssl.enable=true

# ????????
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000
spring.mail.properties.mail.debug=true

# ??????
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.port=465
spring.mail.properties.mail.smtp.ssl.protocols=TLSv1.2

spring.profiles.active=dev




