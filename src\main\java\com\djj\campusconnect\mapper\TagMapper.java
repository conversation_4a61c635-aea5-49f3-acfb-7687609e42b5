package com.djj.campusconnect.mapper;

import com.djj.campusconnect.pojo.Tag;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TagMapper {

    @Select("SELECT * FROM tag WHERE tag_id = #{tagId}")
    public Tag getTagById(Integer tagId);

    @Insert("INSERT INTO tag(tag_id, name, type, created_at) VALUES (#{tagId}, #{name}, #{type}, NOW())")
    public Integer insertTag(Tag tag);

    @Delete("DELETE FROM tag WHERE tag_id = #{tagId}")
    public Integer deleteTagById(Integer tagId);

    @Delete("DELETE FROM tag WHERE name = #{name}")
    public Integer deleteTagByName(String name);

    @Select("SELECT * FROM tag")
    public List<Tag> getAllTags();

    public Integer insertPostTag(Integer postId, List<Integer> tagIds);

}
