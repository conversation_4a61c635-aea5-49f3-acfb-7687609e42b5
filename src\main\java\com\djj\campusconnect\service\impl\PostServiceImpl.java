package com.djj.campusconnect.service.impl;

import com.djj.campusconnect.mapper.PostMapper;
import com.djj.campusconnect.pojo.Post;
import com.djj.campusconnect.service.PostService;
import com.djj.campusconnect.utils.Rank;
import com.djj.campusconnect.utils.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PostServiceImpl implements PostService {

    @Autowired
    PostMapper postMapper;

    @Autowired
    UserContext userContext;

    @Autowired
    Rank rank;

    public Post getPostById(Integer postId) {
        return postMapper.getPostById(postId);
    }

    public List<Post> getAllPost() {
        return postMapper.getAllPost();
    }

    public Boolean verifyPostOfCurrentUser(Integer postId) {
        Post post = postMapper.getPostById(postId);
        if (post == null) {
            return false;
        } else if (userContext.getCurrentUser() == null) {
            return false;
        } else if (post.getUserId() != userContext.getCurrentUser().getUserId()) {
            return false;
        }
        return true;
    }

    @Override
    public List<Integer> getHotPost(Integer start, Integer postNum) {
        return rank.getHotPostsRanking(start, postNum);
    }

    @Override
    public List<Integer> getNewPost(Integer start, Integer postNum) {
        return rank.getLatestPostsRanking(start, postNum);
    }

    @Override
    public Integer addPost(Post post) {
        post.setUserId(userContext.getCurrentUser().getUserId());
        return postMapper.addPost(post);
    }

}
