package com.djj.campusconnect.controller;

import com.djj.campusconnect.pojo.PasswordResetRequest;
import com.djj.campusconnect.pojo.ResetPasswordRequest;
import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.pojo.VerifyCodeRequest;
import com.djj.campusconnect.service.PasswordResetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 密码重置相关接口
 */
@Slf4j
@RestController
@RequestMapping("/password")
public class PasswordResetController {

    @Autowired
    private PasswordResetService passwordResetService;

    /**
     * 请求发送密码重置验证码
     * @param request 包含邮箱的请求
     * @return 发送结果
     */
    @PostMapping("/forgot")
    public Result requestPasswordReset(@RequestBody PasswordResetRequest request) {
        if (request.getEmail() == null || request.getEmail().isEmpty()) {
            return Result.error(400, "邮箱不能为空");
        }
        
        Boolean result = passwordResetService.sendPasswordResetCode(request.getEmail());
        if (result) {
            return Result.success("验证码已发送到您的邮箱");
        } else {
            return Result.error(400, "邮箱不存在或发送失败");
        }
    }
    
    /**
     * 验证邮箱验证码
     * @param request 包含邮箱和验证码的请求
     * @return 验证结果
     */
    @PostMapping("/verify")
    public Result verifyCode(@RequestBody VerifyCodeRequest request) {
        if (request.getEmail() == null || request.getEmail().isEmpty() ||
            request.getCode() == null || request.getCode().isEmpty()) {
            return Result.error(400, "邮箱或验证码不能为空");
        }
        
        Boolean result = passwordResetService.verifyEmailCode(request);
        if (result) {
            return Result.success("验证码正确");
        } else {
            return Result.error(400, "验证码错误或已过期");
        }
    }
    
    /**
     * 重置密码
     * @param request 包含邮箱、验证码和新密码的请求
     * @return 重置结果
     */
    @PostMapping("/reset")
    public Result resetPassword(@RequestBody ResetPasswordRequest request) {
        if (request.getEmail() == null || request.getEmail().isEmpty() ||
            request.getCode() == null || request.getCode().isEmpty() ||
            request.getNewPassword() == null || request.getNewPassword().isEmpty()) {
            return Result.error(400, "邮箱、验证码或新密码不能为空");
        }
        
        Boolean result = passwordResetService.resetPassword(request);
        if (result) {
            return Result.success("密码重置成功");
        } else {
            return Result.error(400, "密码重置失败，请确认邮箱和验证码正确");
        }
    }
}
