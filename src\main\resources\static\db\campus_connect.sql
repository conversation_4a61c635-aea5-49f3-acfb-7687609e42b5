CREATE DATABASE IF NOT EXISTS campus_connect;

#用户表
CREATE TABLE `user` (
                        `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                        `email` VARCHAR(25) NOT NULL COMMENT '邮箱',
                        `password_hash` VARCHAR(128) NOT NULL COMMENT '密码哈希',
                        `nickname` VARCHAR(50) NOT NULL COMMENT '昵称',
                        `avatar_url` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
                        `auth_status` TINYINT NOT NULL DEFAULT 0 COMMENT '认证状态(0:未认证,1:已认证,2:认证中)',
                        `account_status` TINYINT NOT NULL DEFAULT 0 COMMENT '账号状态(0:正常,1:禁用)',
                        `department` VARCHAR(50) DEFAULT NULL COMMENT '院系',
                        `school` VARCHAR(20) DEFAULT NULL COMMENT '学校',
                        `before_card_url` VARCHAR(255) DEFAULT NULL COMMENT '校园卡正面图片URL',
                        `after_card_url` VARCHAR(255) DEFAULT NULL COMMENT '校园卡反面图片URL',
                        `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                        PRIMARY KEY (`user_id`),
                        UNIQUE KEY `uk_email` (`email`),
                        UNIQUE KEY `uk_nickname` (`nickname`),
                        KEY `idx_auth_status` (`auth_status`),
                        KEY `idx_department` (`department`),
                        KEY `idx_school` (`school`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

#动态表
CREATE TABLE `post` (
                        `post_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '动态ID',
                        `user_id` BIGINT NOT NULL COMMENT '发布者ID',
                        `content` TEXT COMMENT '动态内容',
                        `like_num` INT NOT NULL DEFAULT 0 COMMENT '点赞数',
                        `comment_num` INT NOT NULL DEFAULT 0 COMMENT '评论数',
                        `hot_score` DOUBLE NOT NULL DEFAULT 0 COMMENT '热度分数',
                        `visibility` TINYINT NOT NULL DEFAULT 0 COMMENT '可见性(0:公开,1:好友)',
                        `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                        PRIMARY KEY (`post_id`),
                        KEY `idx_user_id` (`user_id`),
                        KEY `idx_created_at` (`created_at`),
                        KEY `idx_visibility` (`visibility`),
                        CONSTRAINT `fk_post_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动态表';

#动态媒体表
CREATE TABLE `post_media` (
                              `media_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '媒体ID',
                              `post_id` BIGINT NOT NULL COMMENT '关联动态ID',
                              `media_url` VARCHAR(255) NOT NULL COMMENT '媒体文件URL',
                              `media_type` TINYINT NOT NULL COMMENT '媒体类型(0:图片,1:视频)',
                              `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
                              `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              PRIMARY KEY (`media_id`),
                              KEY `idx_post_id` (`post_id`),
                              KEY `idx_media_type` (`media_type`),
                              CONSTRAINT `fk_media_post_id` FOREIGN KEY (`post_id`) REFERENCES `post` (`post_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动态媒体表';

#评论表
CREATE TABLE `comment` (
                           `comment_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '评论ID',
                           `post_id` BIGINT NOT NULL COMMENT '关联动态ID',
                           `user_id` BIGINT NOT NULL COMMENT '评论者ID',
                           `parent_comment_id` BIGINT DEFAULT NULL COMMENT '回复的评论ID',
                           `content` TEXT NOT NULL COMMENT '评论内容',
                           `like_num` INT NOT NULL DEFAULT 0 COMMENT '点赞数',
                           `comment_num` INT NOT NULL DEFAULT 0 COMMENT '评论数',
                           `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                           PRIMARY KEY (`comment_id`),
                           KEY `idx_post_id` (`post_id`),
                           KEY `idx_user_id` (`user_id`),
                           KEY `idx_parent_comment_id` (`parent_comment_id`),
                           KEY `idx_created_at` (`created_at`),
                           CONSTRAINT `fk_comment_post_id` FOREIGN KEY (`post_id`) REFERENCES `post` (`post_id`) ON DELETE CASCADE,
                           CONSTRAINT `fk_comment_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
                           CONSTRAINT `fk_comment_parent_id` FOREIGN KEY (`parent_comment_id`) REFERENCES `comment` (`comment_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

#点赞表
CREATE TABLE `like` (
                        `like_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
                        `user_id` BIGINT NOT NULL COMMENT '点赞用户ID',
                        `target_id` BIGINT NOT NULL COMMENT '点赞目标ID',
                        `target_type` TINYINT NOT NULL COMMENT '目标类型(0:动态,1:评论)',
                        `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        PRIMARY KEY (`like_id`),
                        UNIQUE KEY `uk_user_target` (`user_id`, `target_id`, `target_type`),
                        KEY `idx_target_id_type` (`target_id`, `target_type`),
                        KEY `idx_created_at` (`created_at`),
                        CONSTRAINT `fk_like_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点赞表';

#好友关系表
CREATE TABLE `friendship` (
                              `friendship_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '好友关系ID',
                              `user_id1` BIGINT NOT NULL COMMENT '用户1 ID',
                              `user_id2` BIGINT NOT NULL COMMENT '用户2 ID',
                              `status` TINYINT NOT NULL DEFAULT 0 COMMENT '关系状态(0:正常)',
                              `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              PRIMARY KEY (`friendship_id`),
                              UNIQUE KEY `uk_user_pair` (`user_id1`, `user_id2`),
                              KEY `idx_user_id2` (`user_id2`),
                              CONSTRAINT `fk_friendship_user_id1` FOREIGN KEY (`user_id1`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
                              CONSTRAINT `fk_friendship_user_id2` FOREIGN KEY (`user_id2`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
                              CONSTRAINT `chk_user_order` CHECK (`user_id1` < `user_id2`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='好友关系表';

#好友请求表
CREATE TABLE `friend_request` (
                                  `request_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '请求ID',
                                  `requester_id` BIGINT NOT NULL COMMENT '请求者ID',
                                  `recipient_id` BIGINT NOT NULL COMMENT '接收者ID',
                                  `message` VARCHAR(255) DEFAULT NULL COMMENT '请求消息',
                                  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '请求状态(0:待处理,1:同意,2:拒绝)',
                                  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`request_id`),
                                  KEY `idx_requester_id` (`requester_id`),
                                  KEY `idx_recipient_id` (`recipient_id`),
                                  KEY `idx_status` (`status`),
                                  KEY `idx_created_at` (`created_at`),
                                  CONSTRAINT `fk_request_requester_id` FOREIGN KEY (`requester_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
                                  CONSTRAINT `fk_request_recipient_id` FOREIGN KEY (`recipient_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='好友请求表';

#私信表
CREATE TABLE `message` (
                           `message_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '消息ID',
                           `sender_id` BIGINT NOT NULL COMMENT '发送者ID',
                           `receiver_id` BIGINT NOT NULL COMMENT '接收者ID',
                           `content` TEXT NOT NULL COMMENT '消息内容',
                           `message_type` TINYINT NOT NULL DEFAULT 0 COMMENT '消息类型(0:文本,1:图片)',
                           `read_status` TINYINT NOT NULL DEFAULT 0 COMMENT '读取状态(0:未读,1:已读)',
                           `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           PRIMARY KEY (`message_id`),
                           KEY `idx_sender_receiver` (`sender_id`, `receiver_id`),
                           KEY `idx_receiver_id` (`receiver_id`),
                           KEY `idx_read_status` (`read_status`),
                           KEY `idx_created_at` (`created_at`),
                           CONSTRAINT `fk_message_sender_id` FOREIGN KEY (`sender_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
                           CONSTRAINT `fk_message_receiver_id` FOREIGN KEY (`receiver_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='私信表';

#标签表
CREATE TABLE `tag` (
                       `tag_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '标签ID',
                       `name` VARCHAR(50) NOT NULL COMMENT '标签名称',
                       `type` TINYINT NOT NULL DEFAULT 1 COMMENT '标签类型(0:系统,1:用户)',
                       `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                       PRIMARY KEY (`tag_id`),
                       UNIQUE KEY `uk_name` (`name`),
                       KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

#动态标签关联表
CREATE TABLE `post_tag` (
                            `post_id` BIGINT NOT NULL COMMENT '动态ID',
                            `tag_id` BIGINT NOT NULL COMMENT '标签ID',
                            PRIMARY KEY (`post_id`, `tag_id`),
                            KEY `idx_tag_id` (`tag_id`),
                            CONSTRAINT `fk_post_tag_post_id` FOREIGN KEY (`post_id`) REFERENCES `post` (`post_id`) ON DELETE CASCADE,
                            CONSTRAINT `fk_post_tag_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tag` (`tag_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动态标签关联表';

#用户兴趣标签表
CREATE TABLE `user_interest_tag` (
                                     `user_id` BIGINT NOT NULL COMMENT '用户ID',
                                     `tag_id` BIGINT NOT NULL COMMENT '标签ID',
                                     `score` DOUBLE NOT NULL DEFAULT 1 COMMENT '兴趣分值',
                                     `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     PRIMARY KEY (`user_id`, `tag_id`),
                                     KEY `idx_tag_id` (`tag_id`),
                                     KEY `idx_score` (`score`),
                                     CONSTRAINT `fk_user_tag_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
                                     CONSTRAINT `fk_user_tag_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tag` (`tag_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户兴趣标签表';

#通知表
CREATE TABLE `notification` (
                                `notification_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '通知ID',
                                `user_id` BIGINT NOT NULL COMMENT '接收用户ID',
                                `type` TINYINT NOT NULL COMMENT '通知类型(0:系统,1:好友,2:评论,3:点赞)',
                                `content` TEXT NOT NULL COMMENT '通知内容',
                                `read_status` TINYINT NOT NULL DEFAULT 0 COMMENT '读取状态(0:未读,1:已读)',
                                `related_entity_id` BIGINT DEFAULT NULL COMMENT '关联实体ID',
                                `related_entity_type` TINYINT DEFAULT NULL COMMENT '关联实体类型',
                                `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                PRIMARY KEY (`notification_id`),
                                KEY `idx_user_id` (`user_id`),
                                KEY `idx_type` (`type`),
                                KEY `idx_read_status` (`read_status`),
                                KEY `idx_created_at` (`created_at`),
                                CONSTRAINT `fk_notification_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知表';

#举报表
CREATE TABLE `report` (
                          `report_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '举报ID',
                          `reporter_id` BIGINT NOT NULL COMMENT '举报者ID',
                          `target_id` BIGINT NOT NULL COMMENT '被举报目标ID',
                          `target_type` TINYINT NOT NULL COMMENT '目标类型(0:用户,1:动态,2:评论)',
                          `reason` TEXT NOT NULL COMMENT '举报原因',
                          `status` TINYINT NOT NULL DEFAULT 0 COMMENT '处理状态(0:待处理,1:有效,2:无效)',
                          `handled_by_admin_id` BIGINT DEFAULT NULL COMMENT '处理管理员ID',
                          `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                          `handled_at` DATETIME DEFAULT NULL COMMENT '处理时间',
                          PRIMARY KEY (`report_id`),
                          KEY `idx_reporter_id` (`reporter_id`),
                          KEY `idx_target_id_type` (`target_id`, `target_type`),
                          KEY `idx_status` (`status`),
                          KEY `idx_created_at` (`created_at`),
                          KEY `idx_handled_by_admin_id` (`handled_by_admin_id`),
                          CONSTRAINT `fk_report_reporter_id` FOREIGN KEY (`reporter_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
                          CONSTRAINT `fk_report_admin_id` FOREIGN KEY (`handled_by_admin_id`) REFERENCES `admin` (`admin_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='举报表';

#管理员表
CREATE TABLE `admin` (
                                 `admin_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
                                 `username` VARCHAR(50) NOT NULL COMMENT '管理员用户名',
                                 `email` VARCHAR(25) NOT NULL COMMENT '邮箱',
                                 `password_hash` VARCHAR(128) NOT NULL COMMENT '密码哈希',
                                 `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`admin_id`),
                                 UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

#系统配置表
CREATE TABLE `system_config` (
                                 `config_key` VARCHAR(50) NOT NULL COMMENT '配置键',
                                 `config_value` TEXT NULL COMMENT '配置值',
                                 `description` VARCHAR(255) NULL COMMENT '配置描述',
                                 `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

#审计日志表
CREATE TABLE `audit_log` (
                             `log_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
                             `admin_id` BIGINT NOT NULL COMMENT '操作管理员ID',
                             `action` VARCHAR(50) NOT NULL COMMENT '操作类型',
                             `target_entity` VARCHAR(50) NOT NULL COMMENT '操作目标实体',
                             `target_id` BIGINT NULL COMMENT '操作目标ID',
                             `details` TEXT NULL COMMENT '操作详情',
                             `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             PRIMARY KEY (`log_id`),
                             KEY `idx_admin_id` (`admin_id`),
                             KEY `idx_action` (`action`),
                             KEY `idx_target_entity` (`target_entity`),
                             KEY `idx_created_at` (`created_at`),
                             CONSTRAINT `fk_audit_admin_id` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`admin_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审计日志表';

#用户设置表
CREATE TABLE `user_settings` (
                                 `setting_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '设置ID',
                                 `user_id` BIGINT NOT NULL COMMENT '用户ID',
                                 `setting_key` VARCHAR(50) NOT NULL COMMENT '设置键',
                                 `setting_value` VARCHAR(255) NULL COMMENT '设置值',
                                 `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`setting_id`),
                                 UNIQUE KEY `uk_user_key` (`user_id`, `setting_key`),
                                 CONSTRAINT `fk_settings_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户设置表';