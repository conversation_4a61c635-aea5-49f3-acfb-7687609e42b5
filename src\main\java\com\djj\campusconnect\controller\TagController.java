package com.djj.campusconnect.controller;

import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.pojo.Tag;
import com.djj.campusconnect.service.TagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/tag")
public class TagController {

    @Autowired
    private TagService tagService;

    @GetMapping("/all")
    public Result getAllTags() {
        return Result.success(tagService.findAllTags());
    }

    @PostMapping("/create")
    public Result createTag(@RequestBody Tag tag) {
        int result = tagService.addTag(tag);
        if (result == 1) {
            return Result.success(tag);
        } else {
            return Result.error(400, "标签创建失败");
        }
    }

    @PostMapping("/addTagToPost")
    public Result addTagToPost(@RequestParam List<Integer> tagIds, @RequestParam Integer postId) {

    }

}
