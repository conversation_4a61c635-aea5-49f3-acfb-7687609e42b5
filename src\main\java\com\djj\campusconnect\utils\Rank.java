package com.djj.campusconnect.utils;

import com.djj.campusconnect.pojo.Post;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class Rank {

    @Autowired
    private RedisTemplate redisTemplate;

    private static final String HOT_RANK_KEY = "hot:rank";

    private static final String Time_RANK_KEY = "time:rank";

    private static final long EXPIRATION = 60 * 60;

    public void createHotPostsRankingToRedis(List<Post> posts) {
        for (int i = 0; i < posts.size(); i++) {
            Post post = posts.get(i);
            redisTemplate.opsForZSet().add(HOT_RANK_KEY, post.getPostId(), post.getHotScore());
        }
        redisTemplate.expire(HOT_RANK_KEY, EXPIRATION, TimeUnit.SECONDS);
    }
    // 60分钟

    public void updateHotPostsRanking(List<Post> posts) {
        redisTemplate.delete(HOT_RANK_KEY);
        for (int i = 0; i < posts.size(); i++) {
            Post post = posts.get(i);
            redisTemplate.opsForZSet().add(HOT_RANK_KEY, post.getPostId(), post.getHotScore());
        }
        redisTemplate.expire(HOT_RANK_KEY, EXPIRATION, TimeUnit.SECONDS);
    }

    public void createTimePostsRankingToRedis(List<Post> posts) {
        for (int i = 0; i < posts.size(); i++) {
            Post post = posts.get(i);
            long timestamp = post.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            redisTemplate.opsForZSet().add(Time_RANK_KEY, post.getPostId(), timestamp);
        }
        redisTemplate.expire(Time_RANK_KEY, EXPIRATION, TimeUnit.SECONDS);
    }

    public void updateTimePostsRanking(List<Post> posts) {
        redisTemplate.delete(Time_RANK_KEY);
        for (int i = 0; i < posts.size(); i++) {
            Post post = posts.get(i);
            long timestamp = post.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            redisTemplate.opsForZSet().add(Time_RANK_KEY, post.getPostId(), timestamp);
        }
        redisTemplate.expire(Time_RANK_KEY, EXPIRATION, TimeUnit.SECONDS);
    }

    public List<Integer> getHotPostsRanking(int start, int postNum) {
        Set<String> postIdSet = redisTemplate.opsForZSet().reverseRange(HOT_RANK_KEY, start, start + postNum - 1);
        List<Integer> postIds = new ArrayList<>();
        
        if (postIdSet != null && !postIdSet.isEmpty()) {
            for (String idStr : postIdSet) {
                try {
                    postIds.add(Integer.parseInt(idStr));
                } catch (NumberFormatException e) {
                    log.error("解析帖子ID失败: {}", idStr, e);
                }
            }
            log.info("从热门榜获取 {} 条帖子ID", postIds.size());
        } else {
            log.warn("热门榜为空或不存在");
        }
        return postIds;
    }

    public List<Integer> getLatestPostsRanking(int start, int postNum) {
        Set<String> postIdSet = redisTemplate.opsForZSet().reverseRange(Time_RANK_KEY, start, start + postNum - 1);
        List<Integer> postIds = new ArrayList<>();
        
        if (postIdSet != null && !postIdSet.isEmpty()) {
            for (String idStr : postIdSet) {
                try {
                    postIds.add(Integer.parseInt(idStr));
                } catch (NumberFormatException e) {
                    log.error("解析帖子ID失败: {}", idStr, e);
                }
            }
            log.info("从最新榜获取 {} 条帖子ID", postIds.size());
        } else {
            log.warn("最新榜为空或不存在");
        }
        return postIds;
    }

}
