package com.djj.campusconnect.mapper;

import com.djj.campusconnect.pojo.Admin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface AdminMapper {

    @Select("SELECT * FROM admin WHERE email = #{email}")
    public Admin getAdminByEmail(String email);

    @Select("SELECT * FROM admin WHERE admin_id = #{id}")
    public Admin getAdminById(Integer id);
}
