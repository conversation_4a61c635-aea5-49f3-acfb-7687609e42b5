# Redis使用指南 - Campus Connect项目

## 一、Redis基础知识

### 1.1 什么是Redis？

Redis (Remote Dictionary Server) 是一个开源的、基于内存的数据结构存储系统，可以用作数据库、缓存和消息中间件。它支持多种数据类型，如字符串、哈希表、列表、集合、有序集合、位图等。Redis的主要特点是高性能、持久化、支持多种数据结构、原子性操作以及支持分布式环境。

### 1.2 为什么选择Redis？

- **高性能**：基于内存操作，读写速度极快（通常每秒可执行约10万次读写操作）
- **丰富的数据结构**：支持多种数据类型，适用于不同场景
- **持久化**：支持数据持久化，可以将内存中的数据保存到磁盘
- **原子操作**：Redis的操作都是原子性的，多个操作也可以通过事务机制保证原子性
- **支持分布式**：支持主从复制、哨兵模式和集群模式，提高系统可用性

### 1.3 Redis主要数据类型

1. **String（字符串）**：最基本的数据类型，可以存储字符串、整数或浮点数
2. **Hash（哈希）**：键值对集合，适合存储对象
3. **List（列表）**：有序的字符串列表，支持从两端添加或删除元素
4. **Set（集合）**：无序的字符串集合，不允许重复成员
5. **Sorted Set（有序集合）**：类似Set，但每个成员关联一个分数，用于排序
6. **Bitmap（位图）**：可以对字符串按位操作
7. **HyperLogLog**：用于基数统计，非常节省内存
8. **Geo（地理空间）**：用于存储地理位置信息

## 二、在Campus Connect项目中使用Redis

根据项目需求，我们将Redis用于以下几个主要场景：

### 2.1 缓存热点数据

#### 用户信息缓存

```java
// 用户信息缓存示例
@Service
public class UserCacheService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    // 缓存前缀
    private static final String USER_CACHE_PREFIX = "user:";
    // 缓存过期时间（30分钟）
    private static final long CACHE_EXPIRATION = 30 * 60;
    
    /**
     * 将用户信息存入缓存
     */
    public void cacheUserInfo(User user) {
        if (user != null) {
            String key = USER_CACHE_PREFIX + user.getUserId();
            String userJson = JSON.toJSONString(user);
            redisTemplate.opsForValue().set(key, userJson, CACHE_EXPIRATION, TimeUnit.SECONDS);
        }
    }
    
    /**
     * 从缓存获取用户信息
     */
    public User getUserFromCache(Integer userId) {
        String key = USER_CACHE_PREFIX + userId;
        String userJson = redisTemplate.opsForValue().get(key);
        if (userJson != null) {
            return JSON.parseObject(userJson, User.class);
        }
        return null;
    }
    
    /**
     * 删除用户缓存
     */
    public void deleteUserCache(Integer userId) {
        String key = USER_CACHE_PREFIX + userId;
        redisTemplate.delete(key);
    }
}
```

这种方式可以大大减少数据库的访问次数，提高系统性能。当用户信息更新时，记得同时更新缓存。

#### 动态列表缓存

```java
@Service
public class DynamicCacheService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    // 动态列表缓存前缀
    private static final String DYNAMIC_LIST_CACHE_PREFIX = "dynamic:list:";
    // 缓存过期时间（10分钟）
    private static final long CACHE_EXPIRATION = 10 * 60;
    
    /**
     * 缓存动态列表（如热门动态、最新动态等）
     */
    public void cacheDynamicList(String listType, List<Dynamic> dynamics) {
        String key = DYNAMIC_LIST_CACHE_PREFIX + listType;
        String dynamicsJson = JSON.toJSONString(dynamics);
        redisTemplate.opsForValue().set(key, dynamicsJson, CACHE_EXPIRATION, TimeUnit.SECONDS);
    }
    
    /**
     * 获取缓存的动态列表
     */
    public List<Dynamic> getDynamicListFromCache(String listType) {
        String key = DYNAMIC_LIST_CACHE_PREFIX + listType;
        String dynamicsJson = redisTemplate.opsForValue().get(key);
        if (dynamicsJson != null) {
            return JSON.parseArray(dynamicsJson, Dynamic.class);
        }
        return null;
    }
}
```

### 2.2 管理WebSocket连接状态

在即时通讯功能中，使用Redis存储用户ID与WebSocket会话ID的映射关系：

```java
@Service
public class WebSocketSessionManager {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    // WebSocket会话映射前缀
    private static final String WEBSOCKET_SESSION_PREFIX = "ws:session:";
    
    /**
     * 保存用户的WebSocket会话ID
     */
    public void saveUserSession(Integer userId, String sessionId) {
        String key = WEBSOCKET_SESSION_PREFIX + userId;
        redisTemplate.opsForValue().set(key, sessionId);
    }
    
    /**
     * 获取用户的WebSocket会话ID
     */
    public String getUserSessionId(Integer userId) {
        String key = WEBSOCKET_SESSION_PREFIX + userId;
        return redisTemplate.opsForValue().get(key);
    }
    
    /**
     * 移除用户的WebSocket会话
     */
    public void removeUserSession(Integer userId) {
        String key = WEBSOCKET_SESSION_PREFIX + userId;
        redisTemplate.delete(key);
    }
}
```

这样设计可以实现跨服务器的WebSocket消息推送，提升系统的可扩展性。

### 2.3 实现消息队列

使用Redis的List数据结构实现简单的消息队列，用于处理通知推送：

```java
@Service
public class NotificationQueueService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    // 通知队列key
    private static final String NOTIFICATION_QUEUE_KEY = "queue:notification";
    
    /**
     * 将通知消息加入队列
     */
    public void pushNotification(Notification notification) {
        String notificationJson = JSON.toJSONString(notification);
        redisTemplate.opsForList().rightPush(NOTIFICATION_QUEUE_KEY, notificationJson);
    }
    
    /**
     * 从队列获取通知消息
     */
    public Notification popNotification() {
        String notificationJson = redisTemplate.opsForList().leftPop(NOTIFICATION_QUEUE_KEY);
        if (notificationJson != null) {
            return JSON.parseObject(notificationJson, Notification.class);
        }
        return null;
    }
}
```

### 2.4 存储推荐内容

基于标签的内容推荐系统中，使用Redis缓存推荐结果：

```java
@Service
public class RecommendationService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    // 推荐内容缓存前缀
    private static final String RECOMMENDED_DYNAMICS_PREFIX = "recommend:user:";
    // 缓存过期时间（1小时）
    private static final long CACHE_EXPIRATION = 60 * 60;
    
    /**
     * 缓存用户的推荐动态ID列表
     */
    public void cacheRecommendedDynamics(Integer userId, List<Integer> dynamicIds) {
        String key = RECOMMENDED_DYNAMICS_PREFIX + userId;
        String idsJson = JSON.toJSONString(dynamicIds);
        redisTemplate.opsForValue().set(key, idsJson, CACHE_EXPIRATION, TimeUnit.SECONDS);
    }
    
    /**
     * 获取缓存的推荐动态ID列表
     */
    public List<Integer> getRecommendedDynamicsFromCache(Integer userId) {
        String key = RECOMMENDED_DYNAMICS_PREFIX + userId;
        String idsJson = redisTemplate.opsForValue().get(key);
        if (idsJson != null) {
            return JSON.parseArray(idsJson, Integer.class);
        }
        return null;
    }
}
```

### 2.5 计数器和限流

使用Redis实现点赞计数、评论计数等功能，以及API接口的限流：

```java
@Service
public class CounterService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    // 点赞计数前缀
    private static final String LIKE_COUNT_PREFIX = "count:like:";
    // 评论计数前缀
    private static final String COMMENT_COUNT_PREFIX = "count:comment:";
    
    /**
     * 增加点赞计数
     */
    public Long incrementLikeCount(Integer dynamicId) {
        String key = LIKE_COUNT_PREFIX + dynamicId;
        return redisTemplate.opsForValue().increment(key);
    }
    
    /**
     * 减少点赞计数
     */
    public Long decrementLikeCount(Integer dynamicId) {
        String key = LIKE_COUNT_PREFIX + dynamicId;
        return redisTemplate.opsForValue().decrement(key);
    }
    
    /**
     * 获取点赞计数
     */
    public Long getLikeCount(Integer dynamicId) {
        String key = LIKE_COUNT_PREFIX + dynamicId;
        String count = redisTemplate.opsForValue().get(key);
        return count != null ? Long.parseLong(count) : 0L;
    }
    
    /**
     * 增加评论计数
     */
    public Long incrementCommentCount(Integer dynamicId) {
        String key = COMMENT_COUNT_PREFIX + dynamicId;
        return redisTemplate.opsForValue().increment(key);
    }
    
    /**
     * 获取评论计数
     */
    public Long getCommentCount(Integer dynamicId) {
        String key = COMMENT_COUNT_PREFIX + dynamicId;
        String count = redisTemplate.opsForValue().get(key);
        return count != null ? Long.parseLong(count) : 0L;
    }
}
```

### 2.6 分布式锁

使用Redis实现分布式锁，保证在分布式环境中的数据一致性：

```java
@Service
public class DistributedLockService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    // 锁前缀
    private static final String LOCK_PREFIX = "lock:";
    // 默认锁过期时间（10秒）
    private static final long DEFAULT_LOCK_TIMEOUT = 10 * 1000;
    
    /**
     * 尝试获取分布式锁
     * @param lockKey 锁的key
     * @param requestId 请求标识，用于释放锁时确认是否为加锁者
     * @param expireTime 锁的过期时间（毫秒）
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey, String requestId, long expireTime) {
        String key = LOCK_PREFIX + lockKey;
        Boolean success = redisTemplate.opsForValue().setIfAbsent(key, requestId, expireTime, TimeUnit.MILLISECONDS);
        return success != null && success;
    }
    
    /**
     * 释放分布式锁
     * @param lockKey 锁的key
     * @param requestId 请求标识，确保只有加锁者才能释放锁
     * @return 是否成功释放锁
     */
    public boolean releaseLock(String lockKey, String requestId) {
        String key = LOCK_PREFIX + lockKey;
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Long result = redisTemplate.execute(new DefaultRedisScript<>(script, Long.class), 
                Collections.singletonList(key), requestId);
        return result != null && result == 1L;
    }
}
```

## 三、Redis配置

在Spring Boot项目中，Redis的配置相对简单：

### 3.1 添加依赖

在`pom.xml`文件中添加Redis依赖：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- 使用lettuce作为Redis客户端 -->
<dependency>
    <groupId>io.lettuce</groupId>
    <artifactId>lettuce-core</artifactId>
</dependency>

<!-- JSON处理工具，用于对象序列化 -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson</artifactId>
    <version>1.2.83</version>
</dependency>
```

### 3.2 Redis连接配置

在`application.properties`或`application.yml`中配置Redis连接：

```properties
# Redis配置
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=  # 如果有密码，请设置
spring.data.redis.database=0
# 连接池配置
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0
spring.data.redis.lettuce.pool.max-wait=-1ms
```

### 3.3 Redis配置类

创建Redis配置类，自定义RedisTemplate：

```java
@Configuration
public class RedisConfig {
    
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 使用Jackson2JsonRedisSerializer序列化值
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, 
                ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
        serializer.setObjectMapper(mapper);
        
        // 使用StringRedisSerializer序列化键
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setValueSerializer(serializer);
        template.setHashValueSerializer(serializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    /**
     * 配置缓存管理器
     */
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        // 使用RedisCacheManager作为缓存管理器
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))  // 设置默认过期时间为30分钟
                .disableCachingNullValues()  // 不缓存空值
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(defaultCacheConfig)
                .build();
    }
}
```

## 四、Redis最佳实践

### 4.1 缓存更新策略

在使用Redis作为缓存时，有几种常见的缓存更新策略：

1. **Cache-Aside Pattern（旁路缓存模式）**:
   - 读取数据：先查缓存，缓存未命中则查数据库，并将结果存入缓存
   - 更新数据：先更新数据库，然后使缓存失效（删除缓存）

2. **Read/Write Through Pattern（读写穿透模式）**:
   - 读取数据：应用程序从缓存读取，缓存负责从数据源加载数据
   - 更新数据：应用程序更新缓存，缓存负责同步更新数据源

3. **Write Behind Caching Pattern（异步缓存写入模式）**:
   - 更新先写入缓存，然后异步批量更新数据库

在Campus Connect项目中，我们主要使用Cache-Aside模式。

### 4.2 缓存穿透、缓存击穿、缓存雪崩防护

为了提高系统的稳定性，需要防范以下缓存问题：

1. **缓存穿透**：请求查询不存在的数据，绕过缓存直接查询数据库
   - 解决方案：对空值进行缓存；使用布隆过滤器

2. **缓存击穿**：热点key过期导致大量请求直接访问数据库
   - 解决方案：设置热点数据永不过期；使用互斥锁（分布式锁）

3. **缓存雪崩**：大量缓存同时过期或Redis服务宕机
   - 解决方案：设置不同的过期时间；服务降级和熔断；Redis高可用部署

### 4.3 Redis持久化

Redis提供了两种持久化机制：

1. **RDB（Redis Database）**：以指定时间间隔生成数据集的快照
2. **AOF（Append Only File）**：记录服务器收到的所有写操作命令

根据项目需求，可以选择合适的持久化策略：

- 如果Redis仅用作缓存，可以不开启持久化
- 如果Redis还存储了一些关键业务数据，建议同时开启RDB和AOF

## 五、总结

在Campus Connect项目中，Redis作为一个强大的工具，主要用于以下场景：

1. 缓存热点数据（用户信息、动态列表、标签）
2. 管理WebSocket连接状态，支持IM功能
3. 实现简单的消息队列，用于通知推送
4. 存储推荐系统的结算结果
5. 实现计数器和限流功能
6. 提供分布式锁机制

通过合理利用Redis，可以显著提高系统性能、降低数据库压力，同时支持更复杂的功能实现。

## 六、拓展阅读

- [Redis官方文档](https://redis.io/documentation)
- [Spring Data Redis文档](https://docs.spring.io/spring-data/redis/docs/current/reference/html/)
- [Redis设计与实现](http://redisbook.com/)
