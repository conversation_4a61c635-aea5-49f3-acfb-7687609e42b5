package com.djj.campusconnect.utils;

import com.alibaba.fastjson.JSON;
import com.djj.campusconnect.pojo.Admin;
import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.pojo.User;
import com.djj.campusconnect.service.AdminService;
import com.djj.campusconnect.service.UserService;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
@Slf4j
public class LoginCheckInterceptor implements HandlerInterceptor {

    @Autowired
    private JWTUtils jwtUtils;
    
    @Autowired
    private UserService userService;

    @Autowired
    private AdminService adminService;

    @Autowired
    private UserContext userContext;

    @Autowired
    private AdminContext adminContext;

    /**
     * 预处理方法，在请求处理之前执行
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param handler  处理器对象
     * @return true表示继续处理，false表示终止处理
     * @throws Exception 异常
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取请求头中的token
        String token = request.getHeader("token");
        Boolean isPassed = true;
        String message = "";
        // 判断token是否存在
        if (token == null || token.isEmpty()) {
            log.info("token is null");
            message = "token is null";
            isPassed = false;
        } else {
            log.info(token);
            // 解析token
            try {
                Claims claims = jwtUtils.parseJWT(token);
                // 从JWT中获取用户ID
                Integer userId = (Integer) claims.get("userId");
                Integer adminId = (Integer) claims.get("adminId");
                if (userId != null) {
                    userContext.setCurrentUser(userId);
                    if (userContext.getCurrentUser() == null) {
                        // 可选：尝试从数据库加载并存入Redis
                        User user = userService.getUserById(userId);
                        if (user != null) {
                            userContext.saveUserToRedis(user);
                            userContext.setCurrentUser(userId);
                        } else {
                            log.warn("用户不存在, ID: {}", userId);
                            isPassed = false;
                            message = "用户不存在, ID:" + userId;
                        }
                    }
                } else if (adminId != null) {
                    adminContext.setCurrentAdmin(adminId);
                    if (adminContext.getCurrentAdmin() == null) {
                        // 可选：尝试从数据库加载并存入Redis
                        Admin admin = adminService.getAdminById(adminId);
                        if (admin != null) {
                            adminContext.saveAdminToRedis(admin);
                            adminContext.setCurrentAdmin(adminId);
                        } else {
                            log.warn("管理员不存在, ID: {}", adminId);
                            isPassed = false;
                            message = "管理员不存在, ID:" + adminId;
                        }
                    }
                } else {
                    log.warn("Token中没有用户ID信息");
                    isPassed = false;
                    message = "Token中没有用户ID信息";
                }
            } catch (Exception e) {
                log.info("jwt parse error");
                isPassed = false;
                message = "jwt parse error";
            }
        }
        if (!isPassed) {
            Result result = Result.error(401, message);
            String json = JSON.toJSONString(result);
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write(json);
            response.getWriter().flush();
            return false;
        }
        return true;
    }
    
    /**
     * 在请求处理完成后执行，用于清理ThreadLocal中的用户信息
     * 防止内存泄漏
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除ThreadLocal中的用户信息
        userContext.remove();
        adminContext.remove();
        log.debug("已清除ThreadLocal中的用户信息");
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
