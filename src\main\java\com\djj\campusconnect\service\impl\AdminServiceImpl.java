package com.djj.campusconnect.service.impl;

import com.djj.campusconnect.mapper.AdminMapper;
import com.djj.campusconnect.pojo.Admin;
import com.djj.campusconnect.service.AdminService;
import com.djj.campusconnect.utils.AdminContext;
import com.djj.campusconnect.utils.JWTUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AdminServiceImpl implements AdminService {

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private JWTUtils jwtUtils;

    @Autowired
    private AdminContext adminContext;

    @Override
    public String login(String email, String password) {
        Admin admin = adminMapper.getAdminByEmail(email);
        if (admin == null) {
            return "管理员不存在";
        }
        String passwordHash = DigestUtils.md5Hex(password);
        if (!admin.getPasswordHash().equals(passwordHash)) {
            return "密码错误";
        }
        // 生成JWT token
        String jwtToken = jwtUtils.generateUserJWT(admin.getAdminId());
        // 将管理员信息存入Redis
        adminContext.saveAdminToRedis(admin);
        adminContext.setCurrentAdmin(admin.getAdminId());
        return jwtToken;
    }

    @Override
    public Admin getAdminById(Integer id) {
        return adminMapper.getAdminById(id);
    }
}
