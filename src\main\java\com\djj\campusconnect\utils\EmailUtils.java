package com.djj.campusconnect.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 邮件工具类
 */
@Slf4j
@Component
public class EmailUtils {

    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String fromEmail;

    // 存储验证码和过期时间
    private final Map<String, VerificationCode> verificationCodes = new HashMap<>();

    // 验证码有效期（5分钟）
    private static final long VERIFICATION_CODE_EXPIRY = 5 * 60 * 1000;

    /**
     * 生成并发送验证码
     * @param email 邮箱地址
     * @return 生成的验证码
     */
    public String sendVerificationCode(String email) {
        // 生成6位随机验证码
        String code = generateRandomCode(6);
        
        // 创建邮件消息
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(fromEmail); // 设置发件人地址
        message.setTo(email);
        message.setSubject("Campus Connect - 密码重置验证码");
        message.setText("您的密码重置验证码是: " + code + "，该验证码5分钟内有效。如非本人操作，请忽略此邮件。");
        
        // 发送邮件
        mailSender.send(message);
        
        // 存储验证码和过期时间
        VerificationCode verificationCode = new VerificationCode(code, System.currentTimeMillis() + VERIFICATION_CODE_EXPIRY);
        verificationCodes.put(email, verificationCode);
        
        return code;
    }
    
    /**
     * 验证验证码
     * @param email 邮箱地址
     * @param code 用户提交的验证码
     * @return 验证是否成功
     */
    public boolean verifyCode(String email, String code) {
        VerificationCode storedCode = verificationCodes.get(email);

        log.info("正确的验证码:"+ storedCode.code + " 实际验证码：" + code);
        
        if (storedCode == null) {
            log.info("验证码为空");
            return false;
        }
        
        // 检查验证码是否过期
        if (System.currentTimeMillis() > storedCode.expiryTime) {
            verificationCodes.remove(email);
            log.info("验证码过期");
            return false;
        }
        
        // 检查验证码是否匹配
        boolean isValid = storedCode.code.equals(code);
        
        // 如果验证通过，从Map中移除该验证码
        if (isValid) {
            verificationCodes.remove(email);
        }
        
        return isValid;
    }
    
    /**
     * 生成随机验证码
     * @param length 验证码长度
     * @return 随机验证码
     */
    private String generateRandomCode(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }
    
    // 内部类，存储验证码和过期时间
    private static class VerificationCode {
        private final String code;
        private final long expiryTime;
        
        public VerificationCode(String code, long expiryTime) {
            this.code = code;
            this.expiryTime = expiryTime;
        }
    }
}
