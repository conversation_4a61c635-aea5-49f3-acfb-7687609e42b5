# 基于标签的推荐系统设计文档

## 目录

1. [标签系统设计](#1-标签系统设计)
2. [推荐算法设计](#2-推荐算法设计)
3. [加权余弦相似度原理](#3-加权余弦相似度原理)
4. [系统实现](#4-系统实现)
5. [性能优化](#5-性能优化)
6. [扩展与改进](#6-扩展与改进)

## 1. 标签系统设计

### 1.1 用户标签数量

- **最佳数量**：用户最多拥有15-20个兴趣标签
- **理由**：
  - 太少（<5）：推荐内容多样性不足
  - 太多（>30）：会稀释用户真实兴趣，降低推荐精准度
  - 15-20个标签能平衡个性化与多样性

### 1.2 标签分数计算

标签分数反映用户对该标签的兴趣程度：

1. **基础分值**：用户手动添加的兴趣标签初始分为10分
2. **行为加权**：
   - 发布带此标签的动态：+3分
   - 点赞带此标签的动态：+2分
   - 评论带此标签的动态：+2分
   - 浏览带此标签的动态：+1分
3. **时间衰减**：标签分数每30天衰减10%，反映兴趣变化
4. **分数上限**：设置100分上限，防止单一标签过度主导

### 1.3 动态标签数量

- **最佳数量**：每个动态最多5个标签
- **理由**：
  - 限制标签数量可以让用户更精准地标记内容
  - 过多标签会导致标签意义稀释
  - 5个足够覆盖一篇动态的主要主题

### 1.4 用户自定义标签

采用**半开放式**标签系统：

1. 允许用户创建自定义标签，但需要审核
2. 系统预设一批常用标签（学科、活动类型、兴趣爱好等）
3. 当用户输入时，优先推荐已有标签
4. 新创建的标签设置使用阈值，当使用量超过阈值时自动加入常用标签库

## 2. 推荐算法设计

### 2.1 基础推荐算法

使用**加权余弦相似度**计算用户与动态的匹配度：

1. **用户兴趣向量**：将用户的标签及其分数构建成向量
2. **动态标签向量**：将动态的标签构建成向量（可以是二元向量或基于标签在动态中的重要性）
3. **相似度计算**：计算两个向量的加权余弦相似度
4. **排序推荐**：按相似度高低排序，推荐给用户

### 2.2 推荐内容不重复策略

1. **已读记录**：维护用户已读动态ID集合（Redis Set）
2. **推荐过滤**：推荐时过滤掉已读内容
3. **时间窗口**：设置滑动时间窗口（如7天），超过窗口的内容可重新推荐
4. **推荐池分层**：
   - 高相似度池（匹配度>0.7）
   - 中相似度池（0.4<匹配度≤0.7）
   - 探索池（随机选取，占比10-20%）

### 2.3 高效实现方案

利用Redis实现高效推荐：

1. 使用Redis Sorted Set存储推荐结果，分数为相似度
2. 使用Redis Set存储用户已读内容
3. 定期批量计算推荐结果，减少实时计算压力
4. 使用热门动态作为冷启动推荐

## 3. 加权余弦相似度原理

### 3.1 向量空间模型

整个推荐机制基于**向量空间模型**，将用户和内容都表示为多维空间中的向量：

- **用户向量**：表示用户对不同标签的兴趣程度
- **内容向量**：表示内容包含的标签及其重要性

在我们的实现中：
- 用户向量是 `Map<标签ID, 兴趣分数>`
- 内容向量是 `Map<标签ID, 标签权重>`

### 3.2 加权余弦相似度计算

#### 数学原理

加权余弦相似度是余弦相似度的一种变体，考虑了向量中各维度的权重。其计算公式为：

```
similarity = dot(userVector, contentVector) / (||userVector|| * ||contentVector||)
```

其中：
- `dot(userVector, contentVector)` 是两个向量的点积
- `||userVector||` 是用户向量的欧几里得范数
- `||contentVector||` 是内容向量的欧几里得范数

#### 计算步骤详解

1. **点积计算**：
   - 点积表示两个向量在相同维度上的乘积之和
   - 只有用户和内容都包含的标签才会对点积有贡献
   - 点积越大，表示用户兴趣和内容特征越匹配

2. **向量范数计算**：
   - 范数表示向量的"长度"
   - 计算方式是各维度值的平方和再开方
   - 范数用于归一化，消除向量大小的影响

3. **相似度计算**：
   - 将点积除以两个向量范数的乘积
   - 结果范围在[-1, 1]之间，实际使用中通常在[0, 1]之间
   - 值越接近1，表示相似度越高

### 3.3 推荐流程原理

#### 核心步骤

1. **用户兴趣建模**：
   - 从数据库获取用户的标签兴趣及分数
   - 构建用户兴趣向量

2. **内容特征提取**：
   - 获取每个内容项的标签
   - 构建内容特征向量

3. **相似度计算**：
   - 计算用户向量与每个内容向量的相似度
   - 筛选相似度超过阈值的内容

4. **排序与推荐**：
   - 按相似度降序排列内容
   - 选取前N个作为推荐结果

### 3.4 算法优势与局限性

#### 优势

1. **个性化**：根据用户的具体兴趣标签推荐内容
2. **可解释性**：推荐结果可以解释为"因为你对X感兴趣"
3. **冷启动友好**：只需要用户标记几个兴趣标签即可开始推荐
4. **计算效率高**：相比复杂的机器学习模型，计算开销小

#### 局限性

1. **标签覆盖问题**：依赖于内容标签的质量和覆盖度
2. **兴趣变化适应**：需要额外机制来捕捉用户兴趣变化
3. **多样性挑战**：可能推荐过于相似的内容
4. **冷启动内容**：新内容没有足够互动难以被推荐

## 4. 系统实现

### 4.1 核心代码实现

#### RecommendationService 类

```java
@Service
public class RecommendationService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Autowired
    private UserInterestTagMapper userInterestTagMapper;
    
    @Autowired
    private PostTagMapper postTagMapper;
    
    @Autowired
    private PostMapper postMapper;
    
    // 推荐内容缓存前缀
    private static final String RECOMMENDED_POSTS_PREFIX = "recommend:user:";
    // 已读内容集合前缀
    private static final String READ_POSTS_PREFIX = "read:user:";
    // 缓存过期时间（1小时）
    private static final long CACHE_EXPIRATION = 60 * 60;
    // 已读内容过期时间（7天）
    private static final long READ_EXPIRATION = 7 * 24 * 60 * 60;
    // 最小推荐相似度阈值
    private static final double MIN_SIMILARITY_THRESHOLD = 0.1;
    
    /**
     * 为用户生成推荐内容
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐的动态列表
     */
    public List<Post> getRecommendedPosts(Long userId, int limit) {
        // 1. 尝试从缓存获取推荐结果
        List<Long> cachedPostIds = getRecommendedPostIdsFromCache(userId);
        if (cachedPostIds != null && !cachedPostIds.isEmpty()) {
            return postMapper.getPostsByIds(cachedPostIds);
        }
        
        // 2. 获取用户已读内容
        Set<String> readPostIds = redisTemplate.opsForSet().members(READ_POSTS_PREFIX + userId);
        Set<Long> readPostIdSet = readPostIds == null ? new HashSet<>() : 
                                  readPostIds.stream().map(Long::parseLong).collect(Collectors.toSet());
        
        // 3. 获取用户兴趣标签向量
        Map<Long, Integer> userTagVector = getUserTagVector(userId);
        if (userTagVector.isEmpty()) {
            // 冷启动：返回热门内容
            return postMapper.getSomePostByHot(limit);
        }
        
        // 4. 获取最近一段时间内的动态（如30天内）
        List<Post> recentPosts = postMapper.getRecentPosts(30);
        
        // 5. 计算每个动态与用户的相似度
        List<PostSimilarity> postSimilarities = new ArrayList<>();
        for (Post post : recentPosts) {
            // 跳过已读内容
            if (readPostIdSet.contains(post.getPostId())) {
                continue;
            }
            
            // 获取动态标签向量
            Map<Long, Integer> postTagVector = getPostTagVector(post.getPostId());
            
            // 计算相似度
            double similarity = calculateWeightedCosineSimilarity(userTagVector, postTagVector);
            
            // 只添加相似度超过阈值的内容
            if (similarity >= MIN_SIMILARITY_THRESHOLD) {
                postSimilarities.add(new PostSimilarity(post.getPostId(), similarity));
            }
        }
        
        // 6. 按相似度排序
        postSimilarities.sort((a, b) -> Double.compare(b.similarity, a.similarity));
        
        // 7. 取前limit个作为推荐结果
        List<Long> recommendedPostIds = postSimilarities.stream()
                .limit(limit)
                .map(ps -> ps.postId)
                .collect(Collectors.toList());
        
        // 8. 缓存推荐结果
        cacheRecommendedPostIds(userId, recommendedPostIds);
        
        // 9. 返回推荐的动态
        return postMapper.getPostsByIds(recommendedPostIds);
    }
    
    /**
     * 计算加权余弦相似度
     */
    private double calculateWeightedCosineSimilarity(Map<Long, Integer> userVector, Map<Long, Integer> postVector) {
        if (userVector.isEmpty() || postVector.isEmpty()) {
            return 0.0;
        }
        
        double dotProduct = 0.0;
        double userNorm = 0.0;
        double postNorm = 0.0;
        
        // 计算点积和用户向量范数
        for (Map.Entry<Long, Integer> userEntry : userVector.entrySet()) {
            Long tagId = userEntry.getKey();
            Integer userScore = userEntry.getValue();
            
            userNorm += userScore * userScore;
            
            if (postVector.containsKey(tagId)) {
                dotProduct += userScore * postVector.get(tagId);
            }
        }
        
        // 计算动态向量范数
        for (Integer weight : postVector.values()) {
            postNorm += weight * weight;
        }
        
        // 计算相似度
        if (userNorm == 0 || postNorm == 0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(userNorm) * Math.sqrt(postNorm));
    }
    
    // 其他辅助方法...
}
```

### 4.2 数据库设计

推荐系统依赖以下数据表：

1. **用户兴趣标签表 (user_interest_tag)**
   ```sql
   CREATE TABLE `user_interest_tag` (
       `user_id` BIGINT NOT NULL COMMENT '用户ID',
       `tag_id` BIGINT NOT NULL COMMENT '标签ID',
       `score` INT NOT NULL DEFAULT 1 COMMENT '兴趣分值',
       `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       PRIMARY KEY (`user_id`, `tag_id`),
       KEY `idx_tag_id` (`tag_id`),
       KEY `idx_score` (`score`),
       CONSTRAINT `fk_user_tag_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
       CONSTRAINT `fk_user_tag_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tag` (`tag_id`) ON DELETE CASCADE
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户兴趣标签表';
   ```

2. **标签表 (tag)**
   ```sql
   CREATE TABLE `tag` (
       `tag_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '标签ID',
       `name` VARCHAR(50) NOT NULL COMMENT '标签名称',
       `type` TINYINT NOT NULL DEFAULT 1 COMMENT '标签类型(0:系统,1:用户)',
       `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       PRIMARY KEY (`tag_id`),
       UNIQUE KEY `uk_name` (`name`),
       KEY `idx_type` (`type`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';
   ```

3. **动态标签表 (post_tag)**
   ```sql
   CREATE TABLE `post_tag` (
       `post_id` BIGINT NOT NULL COMMENT '动态ID',
       `tag_id` BIGINT NOT NULL COMMENT '标签ID',
       `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       PRIMARY KEY (`post_id`, `tag_id`),
       KEY `idx_tag_id` (`tag_id`),
       CONSTRAINT `fk_post_tag_post_id` FOREIGN KEY (`post_id`) REFERENCES `post` (`post_id`) ON DELETE CASCADE,
       CONSTRAINT `fk_post_tag_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tag` (`tag_id`) ON DELETE CASCADE
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动态标签表';
   ```

### 4.3 Redis缓存设计

使用Redis存储以下数据：

1. **推荐结果缓存**
   - Key: `recommend:user:{userId}`
   - Value: 序列化的推荐动态ID列表
   - 过期时间: 1小时

2. **已读内容集合**
   - Key: `read:user:{userId}`
   - Value: 用户已读动态ID的Set集合
   - 过期时间: 7天

3. **标签热度排行**
   - Key: `ranking:hot_tags`
   - Value: 标签ID与热度分数的Sorted Set
   - 无过期时间，定期更新

## 5. 性能优化

### 5.1 缓存策略

1. **多级缓存**：
   - 内存缓存：热门标签、热门动态
   - Redis缓存：推荐结果、用户兴趣向量
   - 数据库：持久化存储

2. **缓存预热**：
   - 系统启动时预加载热门标签
   - 定时任务预计算活跃用户的推荐结果

3. **缓存更新策略**：
   - 用户兴趣变化时主动失效相关缓存
   - 设置合理的缓存过期时间

### 5.2 计算优化

1. **批量计算**：
   - 使用定时任务批量计算推荐结果
   - 减少实时计算压力

2. **增量更新**：
   - 只对新内容计算相似度
   - 合并新旧推荐结果

3. **并行计算**：
   - 使用线程池并行计算多个用户的推荐结果
   - 使用CompletableFuture异步处理

### 5.3 数据库优化

1. **索引优化**：
   - 为常用查询添加合适的索引
   - 避免全表扫描

2. **分页查询**：
   - 使用游标分页代替偏移分页
   - 减少大数据量查询的性能问题

3. **读写分离**：
   - 推荐计算使用只读副本
   - 减轻主库压力

## 6. 扩展与改进

### 6.1 混合推荐策略

实现多种推荐策略的混合：

1. **70% 基于内容的推荐**：使用当前的加权余弦相似度算法
2. **20% 协同过滤推荐**：基于相似用户的行为推荐
3. **10% 探索性推荐**：随机推荐或热门内容推荐

### 6.2 实时兴趣捕捉

改进用户兴趣模型：

1. **短期兴趣**：最近7天的行为权重更高
2. **长期兴趣**：历史累积的稳定兴趣
3. **兴趣衰减**：自动降低长期不互动的标签权重

### 6.3 多样性增强

防止推荐结果过于单一：

1. **标签多样性**：确保推荐结果覆盖多个标签类别
2. **内容类型多样性**：混合图文、纯文本等不同类型内容
3. **时间多样性**：混合新内容和经典内容

### 6.4 高级特性

未来可考虑的高级功能：

1. **上下文感知推荐**：根据时间、地点等上下文调整推荐
2. **A/B测试框架**：测试不同推荐算法的效果
3. **推荐解释**：向用户解释推荐原因，增加透明度
4. **负反馈处理**：允许用户对推荐结果进行反馈，调整算法

---

本文档详细描述了基于标签的推荐系统设计，包括标签系统设计、推荐算法原理、系统实现和性能优化等方面。通过加权余弦相似度算法，系统能够为用户提供个性化的内容推荐，同时保证推荐内容的多样性和新鲜度。