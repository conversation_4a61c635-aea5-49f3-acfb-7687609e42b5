package com.djj.campusconnect.service.impl;

import com.djj.campusconnect.mapper.UserMapper;
import com.djj.campusconnect.pojo.User;
import com.djj.campusconnect.pojo.VerifyCodeRequest;
import com.djj.campusconnect.service.UserService;
import com.djj.campusconnect.utils.EmailUtils;
import com.djj.campusconnect.utils.JWTUtils;
import com.djj.campusconnect.utils.UserContext;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JWTUtils jwtUtils;

    @Autowired
    private UserContext userContext;

    @Autowired
    private EmailUtils emailUtils;

    /**
     * 登录
     *
     * @param email   用户邮箱
     * @param password 密码
     * @return JWT token 或错误信息
     */
    @Override
    public String login(String email, String password) {
        User user = userMapper.getUserByEmail(email);
        if (user == null) {
            return "用户不存在";
        }
        String passwordHash = DigestUtils.md5Hex(password);
        if (!user.getPasswordHash().equals(passwordHash)) {
            return "密码错误";
        }
        if (user.getAccountStatus() == 1) {
            return "账号已冻结";
        }
        // 生成JWT token
        String jwtToken = jwtUtils.generateUserJWT(user.getUserId());
        userContext.saveUserToRedis(user);
        userContext.setCurrentUser(user.getUserId());
        return jwtToken;
    }

    /**
     * 注册
     *
     * @param user 用户信息
     * @return 注册结果
     */
    @Override
    public Integer register(User user, VerifyCodeRequest request) {
        try {
            // 验证邮箱验证码
            if (!emailUtils.verifyCode(request.getEmail(), request.getCode())) {
                return -1;
            }
            user.setPasswordHash(DigestUtils.md5Hex(user.getPasswordHash()));
            return userMapper.createUser(user);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 更新结果
     */
    @Override
    public Boolean updateUser(User user) {
        try {
            userMapper.updateUser(user);
            userContext.updateUserInRedis(user);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @Override
    public User getUserById(Integer userId) {
        return userMapper.getUserById(userId);
    }

    @Override
    public Boolean sendCode(String email) {
        emailUtils.sendVerificationCode(email);
        return true;
    }
}
