#### 5.2.5 DTOu7c7bu8bbeu8ba1

##### 5.2.5.1 PostCreateRequestu7c7b

```java
/**
 * u53d1u5e03u52a8u6001u8bf7u6c42
 */
@Data
public class PostCreateRequest {
    
    /**
     * u52a8u6001u5185u5bb9
     */
    private String content;
    
    /**
     * u52a8u6001u7c7bu578buff0c1=u6587u5b57uff0c2=u56feu6587uff0c3=u89c6u9891
     */
    private Integer postType;
    
    /**
     * u53efu89c1u6027uff0c1=u516cu5f00uff0c2=u597du53cbu53efu89c1uff0c3=u4ec5u81eau5df1u53efu89c1
     */
    private Integer visibility;
    
    /**
     * u5a92u4f53u5217u8868
     */
    private List<MediaRequest> mediaList;
    
    /**
     * u6807u7b7eIDu5217u8868
     */
    private List<Long> tagIds;
}
```

##### 5.2.5.2 MediaRequestu7c7b

```java
/**
 * u5a92u4f53u8bf7u6c42
 */
@Data
public class MediaRequest {
    
    /**
     * u5a92u4f53URL
     */
    private String mediaUrl;
    
    /**
     * u5a92u4f53u7c7bu578buff0c1=u56feu7247uff0c2=u89c6u9891
     */
    private Integer mediaType;
    
    /**
     * u7f29u7565u56feURLuff08u5f53mediaType=2u65f6u6709u503cuff09
     */
    private String thumbnailUrl;
}
```

##### 5.2.5.3 CommentRequestu7c7b

```java
/**
 * u53d1u8868u8bc4u8bbau8bf7u6c42
 */
@Data
public class CommentRequest {
    
    /**
     * u8bc4u8bbau5185u5bb9
     */
    private String content;
    
    /**
     * u7236u8bc4u8bbaIDuff08u56deu590du8bc4u8bbau65f6u6709u503cuff09
     */
    private Long parentId;
    
    /**
     * u88abu56deu590du7684u7528u6237IDuff08u56deu590du8bc4u8bbau65f6u6709u503cuff09
     */
    private Long replyToUserId;
}
```

##### 5.2.5.4 LikeRequestu7c7b

```java
/**
 * u70b9u8d5eu8bf7u6c42
 */
@Data
public class LikeRequest {
    
    /**
     * u662fu5426u70b9u8d5euff0ctrue=u70b9u8d5euff0cfalse=u53d6u6d88u70b9u8d5e
     */
    private Boolean like;
}
```

##### 5.2.5.5 DisinterestRequestu7c7b

```java
/**
 * u4e0du611fu5174u8da3u8bf7u6c42
 */
@Data
public class DisinterestRequest {
    
    /**
     * u539fu56e0u4ee3u7801uff0c1=u4e0du611fu5174u8da3u5185u5bb9uff0c2=u4e0du60f3u770bu8fd9u4e2au4ebau7684u5185u5bb9uff0c3=u5185u5bb9u91cdu590d
     */
    private Integer reason;
    
    /**
     * u5907u6ce8
     */
    private String remark;
}
```

##### 5.2.5.6 ShareRequestu7c7b

```java
/**
 * u5206u4eabu8bf7u6c42
 */
@Data
public class ShareRequest {
    
    /**
     * u597du53cbIDu5217u8868
     */
    private List<Long> friendIds;
    
    /**
     * u5206u4eabu9644u8a00
     */
    private String message;
}
```

##### 5.2.5.7 TagOperationu7c7b

```java
/**
 * u6807u7b7eu64cdu4f5c
 */
@Data
public class TagOperation {
    
    /**
     * u6807u7b7eID
     */
    private Long tagId;
    
    /**
     * u64cdu4f5cu7c7bu578buff0cadd=u6dfbu52a0uff0cremove=u79fbu9664
     */
    private String operation;
    
    /**
     * u5174u8da3u5206u6570uff08u4ec5u5f53operation=addu65f6u6709u6548uff09
     */
    private Integer score;
}
```

##### 5.2.5.8 ShareResultu7c7b

```java
/**
 * u5206u4eabu7ed3u679c
 */
@Data
public class ShareResult {
    
    /**
     * u5206u4eabu603bu6570
     */
    private Integer shareCount;
    
    /**
     * u6210u529fu6570
     */
    private Integer successCount;
    
    /**
     * u5931u8d25u6570
     */
    private Integer failCount;
    
    /**
     * u5206u4eabu65f6u95f4
     */
    private Date shareTime;
}
```

### 5.3 u6570u636eu5e93u8bbeu8ba1

#### 5.3.1 u8868u7ed3u6784u8bbeu8ba1

##### 5.3.1.1 t_postu8868uff08u52a8u6001u8868uff09

```sql
CREATE TABLE `t_post` (
  `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u52a8u6001ID',
  `user_id` bigint(20) NOT NULL COMMENT 'u53d1u5e03u8005ID',
  `content` text COMMENT 'u52a8u6001u5185u5bb9',
  `post_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'u52a8u6001u7c7bu578buff0c1=u6587u5b57uff0c2=u56feu6587uff0c3=u89c6u9891',
  `visibility` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'u53efu89c1u6027uff0c1=u516cu5f00uff0c2=u597du53cbu53efu89c1uff0c3=u4ec5u81eau5df1u53efu89c1',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'u72b6u6001uff0c1=u6b63u5e38uff0c0=u5df2u5220u9664',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT 'u6d4fu89c8u91cf',
  `like_count` int(11) NOT NULL DEFAULT '0' COMMENT 'u70b9u8d5eu6570',
  `comment_count` int(11) NOT NULL DEFAULT '0' COMMENT 'u8bc4u8bbau6570',
  `hot_score` double(10,2) NOT NULL DEFAULT '0.00' COMMENT 'u70edu5ea6u5206u6570',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  `updated_at` datetime NOT NULL COMMENT 'u66f4u65b0u65f6u95f4',
  PRIMARY KEY (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_hot_score` (`hot_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u52a8u6001u8868';
```
