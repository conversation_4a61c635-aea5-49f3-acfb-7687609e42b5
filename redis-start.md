# Redis 入门指南 - Spring Boot 实践

## 1. Redis基础知识

### 1.1 什么是Redis？
Redis (Remote Dictionary Server) 是一个开源的、基于内存的数据结构存储系统，它可以用作数据库、缓存和消息中间件。Redis以其卓越的性能、灵活的数据结构和丰富的功能而闻名。

**核心特点详解**：
- **内存存储**：Redis将所有数据存储在内存中，这是它能够提供超高性能的关键。与传统关系型数据库主要依赖磁盘存储不同，Redis的内存操作速度可以达到每秒10万次读写。
- **数据持久化**：虽然是内存数据库，Redis提供了RDB和AOF两种持久化机制，确保数据在服务器重启后不会丢失。
- **原子性操作**：Redis的单个操作是原子性的，不会被其他客户端的命令中断。这对于计数器、分布式锁等场景至关重要。
- **丰富的数据类型**：不仅支持字符串，还支持更复杂的数据结构，使其能适应各种业务场景。

**Redis vs 传统数据库**：
- Redis是NoSQL数据库，采用键值对存储
- MySQL等关系型数据库使用表格结构和SQL语言
- Redis全部数据在内存，而传统数据库以磁盘为主
- Redis读写性能高达10-100k QPS，而传统数据库通常在1k QPS左右

### 1.2 Redis数据类型详解

Redis支持的主要数据类型及其在Campus Connect项目中的应用场景：

#### 1.2.1 String(字符串)
**特点**：最基础的数据类型，可存储字符串、整数、浮点数。单个字符串最大512MB。

**常用命令**：
- `SET key value` - 设置值
- `GET key` - 获取值
- `INCR key` - 原子自增
- `EXPIRE key seconds` - 设置过期时间

**校园社交平台应用**：
- 存储用户会话信息（例如JWT令牌）
- 用户动态的评论数、点赞数计数器
- 缓存用户配置或系统参数
- 限流器（限制发布动态频率）

```java
// 例：实现动态点赞计数
String likeKey = "post:like:" + postId;
redisTemplate.opsForValue().increment(likeKey);

// 设置用户会话信息并设置过期时间
redisTemplate.opsForValue().set("user:session:" + userId, sessionData, 30, TimeUnit.MINUTES);
```

#### 1.2.2 Hash(哈希)
**特点**：存储对象的理想选择，每个Hash可存储多个字段-值对。比JSON存储更节省空间。

**常用命令**：
- `HSET key field value` - 设置字段值
- `HGET key field` - 获取字段值
- `HMSET key field1 value1 [field2 value2 ...]` - 设置多个字段
- `HGETALL key` - 获取所有字段和值

**校园社交平台应用**：
- 存储用户信息（多个属性）
- 存储动态帖子详情
- 缓存用户的偏好设置
- 存储标签信息

```java
// 例：缓存用户信息
String userKey = "user:" + userId;
Map<String, Object> userMap = new HashMap<>();
userMap.put("nickname", user.getNickname());
userMap.put("avatar", user.getAvatarUrl());
userMap.put("department", user.getDepartment());
redisTemplate.opsForHash().putAll(userKey, userMap);

// 获取用户信息的某个属性
String nickname = (String) redisTemplate.opsForHash().get(userKey, "nickname");
```

#### 1.2.3 List(列表)
**特点**：有序的字符串列表，可以从两端插入和弹出元素。理想用于实现队列和栈。

**常用命令**：
- `LPUSH key value [value ...]` - 左侧插入元素
- `RPUSH key value [value ...]` - 右侧插入元素
- `LPOP key` - 左侧弹出元素
- `RPOP key` - 右侧弹出元素
- `LRANGE key start stop` - 获取指定范围元素

**校园社交平台应用**：
- 用户动态流和时间线
- 用户的消息通知列表
- 实现简单的消息队列（如通知推送队列）
- 存储最近会话历史

```java
// 例：实现通知队列
String notificationQueueKey = "notification:queue";
redisTemplate.opsForList().rightPush(notificationQueueKey, notificationJson); // 添加通知

// 处理通知
String notification = redisTemplate.opsForList().leftPop(notificationQueueKey);

// 获取用户最近10条消息
List<Object> recentMessages = redisTemplate.opsForList().range("user:messages:" + userId, 0, 9);
```

#### 1.2.4 Set(集合)
**特点**：无序、元素唯一的集合。支持集合交、并、差等操作。

**常用命令**：
- `SADD key member [member ...]` - 添加元素
- `SMEMBERS key` - 获取所有元素
- `SISMEMBER key member` - 判断元素是否存在
- `SINTER key [key ...]` - 多个集合的交集
- `SUNION key [key ...]` - 多个集合的并集

**校园社交平台应用**：
- 存储用户的关注者、粉丝、好友集合
- 记录在线用户ID
- 标签系统（用户感兴趣的标签）
- 恢复用户与共同好友（交集操作）

```java
// 例：记录在线用户
String onlineUsersKey = "online:users";
redisTemplate.opsForSet().add(onlineUsersKey, userId.toString());

// 判断用户是否在线
Boolean isOnline = redisTemplate.opsForSet().isMember(onlineUsersKey, userId.toString());

// 恢复共同好友
Set<Object> commonFriends = redisTemplate.opsForSet().intersect("user:friends:" + userId1, "user:friends:" + userId2);
```

#### 1.2.5 Sorted Set(有序集合/ZSet)
**特点**：集合中每个元素关联一个分数，根据分数排序。非常适合排行榜场景。

**常用命令**：
- `ZADD key score member [score member ...]` - 添加元素和分数
- `ZRANGE key start stop [WITHSCORES]` - 按分数升序获取元素
- `ZREVRANGE key start stop [WITHSCORES]` - 按分数降序获取元素
- `ZSCORE key member` - 获取元素分数
- `ZINCRBY key increment member` - 增加元素分数

**校园社交平台应用**：
- 热门帖子排行榜
- 学生活跃度排行
- 按时间戳排序的内容列表
- 标签热度排名

```java
// 例：更新帖子热度分数
String hotPostsKey = "ranking:hot_posts";
// 每次浏览增加1分，评论增加5分，点赞增加3分
redisTemplate.opsForZSet().incrementScore(hotPostsKey, postId, 1.0); // 浏览+1

// 获取热门前10的帖子ID
Set<Object> topPosts = redisTemplate.opsForZSet().reverseRange(hotPostsKey, 0, 9);
```

### 1.3 Redis适用场景

#### 1.3.1 Campus Connect项目中的具体应用

1. **用户会话管理**
   - **问题**：在分布式部署的情况下，用户的登录会话如何在多服务器间共享？
   - **Redis解决方案**：将用户的登录状态存入Redis，使任何服务器都能识别用户身份
   - **效果**：支持系统水平扩展，保持用户登录一致性

2. **实时热榜与推荐**
   - **问题**：如何高效地生成和更新校园热门帖子排行？
   - **Redis解决方案**：使用ZSet存储帖子ID和热度分数，每次互动更新分数
   - **效果**：实时性强，计算负载低，查询性能高

3. **内容互动计数**
   - **问题**：动态的点赞数、评论数频繁变化，直接操作数据库压力大
   - **Redis解决方案**：使用String类型的原子递增/递减操作实现计数，定期同步到数据库
   - **效果**：显著减轻数据库负担，提高并发处理能力

4. **在线用户状态**
   - **问题**：如何追踪和显示当前在线用户？
   - **Redis解决方案**：使用Set存储在线用户ID，用String存储用户详细信息
   - **效果**：实时更新用户状态，快速查询在线用户数量

5. **分布式锁保障数据一致性**
   - **问题**：如何防止多个服务实例同时修改同一资源？
   - **Redis解决方案**：利用Redis的SETNX命令实现分布式锁
   - **效果**：保证在分布式系统中的数据操作原子性和一致性

## 2. Spring Boot集成Redis

### 2.1 集成步骤与细节解析

在Campus Connect这样的校园社交平台中集成Redis非常直接，主要分为以下几个步骤：

#### 2.1.1 添加项目依赖
在`pom.xml`中添加以下依赖：

```xml
<!-- Spring Boot Redis -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- 使用Lettuce作为Redis客户端 -->
<dependency>
    <groupId>io.lettuce</groupId>
    <artifactId>lettuce-core</artifactId>
</dependency>

<!-- 添加连接池支持 -->
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-pool2</artifactId>
</dependency>
```

**依赖说明**：

- `spring-boot-starter-data-redis` - 提供Spring Boot对Redis的自动配置支持，包含RedisTemplate和其他Redis操作工具
- `lettuce-core` - Redis客户端，支持更高级的功能和并发性能（比较老的Jedis更现代）
- `commons-pool2` - 连接池支持，当需要高并发时必需添加

> **注意：** Spring Boot 2.x后默认使用Lettuce作为Redis客户端，而非Jedis。Lettuce基于Netty，支持更高级的并发模型。

### 2.2 Redis配置详解

在`application.properties`或`application.yml`中配置Redis连接参数：

```properties
# Redis服务器基本配置
spring.redis.host=localhost          # Redis服务器地址
spring.redis.port=6379               # Redis服务器端口
spring.redis.password=               # Redis服务器密码（默认为空）
spring.redis.database=0              # Redis数据库索引（0-15）
spring.redis.timeout=10000ms         # 连接超时时间

# 连接池配置参数（重要！影响高并发时性能）
spring.redis.lettuce.pool.max-active=8    # 连接池最大连接数（负值表示无限制）
spring.redis.lettuce.pool.max-wait=-1ms   # 连接池最大阻塞等待时间（负值表示无限制）
spring.redis.lettuce.pool.max-idle=8      # 连接池中的最大空闲连接
spring.redis.lettuce.pool.min-idle=0      # 连接池中的最小空闲连接
```

**配置说明**：

1. **基本连接参数**：
   - `host/port/password` - 指定Redis服务器的位置和认证信息
   - `database` - Redis支持0-15共16个数据库，可以隔离不同环境或应用的数据
   - `timeout` - 连接超时时间，在网络不稳定环境中应适当调高

2. **连接池参数**：
   - `max-active` - 对应应用程序的并发量，过小会导致等待，过大会浪费资源
   - `max-wait` - 连接池耗尽时的等待时间，超过该值则抛出异常
   - `max-idle/min-idle` - 空闲连接的维护数量，影响连接池效率

对于校园社交平台这类高并发应用，强烈建议调整连接池参数以优化性能。

#### 配置示例（开发/测试/生产环境）

```properties
# 开发环境配置
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=0
spring.redis.lettuce.pool.max-active=8

# 测试环境配置
# spring.redis.host=test-redis.example.com
# spring.redis.port=6379
# spring.redis.password=test-password
# spring.redis.database=1
# spring.redis.lettuce.pool.max-active=16

# 生产环境配置
# spring.redis.host=prod-redis.example.com
# spring.redis.port=6379
# spring.redis.password=strong-password
# spring.redis.database=0
# spring.redis.timeout=5000ms
# spring.redis.lettuce.pool.max-active=32
# spring.redis.lettuce.pool.max-wait=1000ms
# spring.redis.lettuce.pool.max-idle=16
# spring.redis.lettuce.pool.min-idle=4
```

### 2.3 自定义Redis配置类
创建一个配置类，定制RedisTemplate：

```java
@Configuration
public class RedisConfig {
    
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        // 创建RedisTemplate对象
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 设置连接工厂
        template.setConnectionFactory(connectionFactory);
        
        // 创建JSON序列化工具
        Jackson2JsonRedisSerializer<Object> jsonSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, 
                ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
        jsonSerializer.setObjectMapper(mapper);
        
        // 创建String序列化工具
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        
        // 设置key和hashKey采用String序列化方式
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        // 设置value和hashValue采用JSON序列化方式
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    /**
     * 配置缓存管理器
     */
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        // 生成默认缓存配置
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))  // 设置缓存有效期30分钟
                .disableCachingNullValues()  // 不缓存空值
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .build();
    }
}
```

## 3. Redis在Spring Boot中的常用操作

### 3.1 字符串操作 (String)
```java
@Service
public class StringOperationsService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 存储字符串值
     */
    public void setString(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }
    
    /**
     * 存储字符串值并设置过期时间
     */
    public void setStringWithExpire(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }
    
    /**
     * 获取字符串值
     */
    public Object getString(String key) {
        return redisTemplate.opsForValue().get(key);
    }
    
    /**
     * 删除字符串值
     */
    public Boolean deleteString(String key) {
        return redisTemplate.delete(key);
    }
    
    /**
     * 递增操作
     */
    public Long increment(String key, long delta) {
        return redisTemplate.opsForValue().increment(key, delta);
    }
}
```

### 3.2 哈希操作 (Hash)
```java
@Service
public class HashOperationsService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 存储单个哈希项
     */
    public void putHashValue(String key, String hashKey, Object value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }
    
    /**
     * 存储多个哈希项
     */
    public void putAllHashValues(String key, Map<String, Object> map) {
        redisTemplate.opsForHash().putAll(key, map);
    }
    
    /**
     * 获取单个哈希项
     */
    public Object getHashValue(String key, String hashKey) {
        return redisTemplate.opsForHash().get(key, hashKey);
    }
    
    /**
     * 获取所有哈希项
     */
    public Map<Object, Object> getAllHashValues(String key) {
        return redisTemplate.opsForHash().entries(key);
    }
    
    /**
     * 删除哈希项
     */
    public Long deleteHashValue(String key, Object... hashKeys) {
        return redisTemplate.opsForHash().delete(key, hashKeys);
    }
}
```

### 3.3 列表操作 (List)
```java
@Service
public class ListOperationsService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 从左侧添加元素
     */
    public Long leftPush(String key, Object value) {
        return redisTemplate.opsForList().leftPush(key, value);
    }
    
    /**
     * 从右侧添加元素
     */
    public Long rightPush(String key, Object value) {
        return redisTemplate.opsForList().rightPush(key, value);
    }
    
    /**
     * 从左侧弹出元素
     */
    public Object leftPop(String key) {
        return redisTemplate.opsForList().leftPop(key);
    }
    
    /**
     * 获取列表范围内的元素
     */
    public List<Object> getRange(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }
    
    /**
     * 获取列表长度
     */
    public Long size(String key) {
        return redisTemplate.opsForList().size(key);
    }
}
```

### 3.4 集合操作 (Set)
```java
@Service
public class SetOperationsService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 添加集合元素
     */
    public Long add(String key, Object... values) {
        return redisTemplate.opsForSet().add(key, values);
    }
    
    /**
     * 获取集合所有元素
     */
    public Set<Object> members(String key) {
        return redisTemplate.opsForSet().members(key);
    }
    
    /**
     * 判断元素是否在集合中
     */
    public Boolean isMember(String key, Object value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }
    
    /**
     * 移除集合元素
     */
    public Long remove(String key, Object... values) {
        return redisTemplate.opsForSet().remove(key, values);
    }
    
    /**
     * 获取集合大小
     */
    public Long size(String key) {
        return redisTemplate.opsForSet().size(key);
    }
}
```

### 3.5 有序集合操作 (Sorted Set)
```java
@Service
public class ZSetOperationsService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 添加元素
     */
    public Boolean add(String key, Object value, double score) {
        return redisTemplate.opsForZSet().add(key, value, score);
    }
    
    /**
     * 获取指定分数范围的元素（按分数升序）
     */
    public Set<Object> rangeByScore(String key, double min, double max) {
        return redisTemplate.opsForZSet().rangeByScore(key, min, max);
    }
    
    /**
     * 获取指定排名范围的元素（按分数升序，0表示第一个元素）
     */
    public Set<Object> range(String key, long start, long end) {
        return redisTemplate.opsForZSet().range(key, start, end);
    }
    
    /**
     * 获取元素的排名（按分数升序，0表示第一个元素）
     */
    public Long rank(String key, Object value) {
        return redisTemplate.opsForZSet().rank(key, value);
    }
    
    /**
     * 获取元素的分数
     */
    public Double score(String key, Object value) {
        return redisTemplate.opsForZSet().score(key, value);
    }
}
```

## 4. Spring Boot中Redis的最佳实践

### 4.1 合理设计键名
```java
// 推荐：使用统一的前缀和分隔符
private static final String USER_KEY_PREFIX = "user:";
private static final String POST_KEY_PREFIX = "post:";

// 使用方式
String userKey = USER_KEY_PREFIX + userId;
String postKey = POST_KEY_PREFIX + postId;
```

### 4.2 使用专门的服务类
```java
@Service
public class UserCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String USER_KEY_PREFIX = "user:";
    private static final long USER_CACHE_EXPIRE = 30; // 30分钟
    
    public void cacheUser(User user) {
        String key = USER_KEY_PREFIX + user.getId();
        redisTemplate.opsForValue().set(key, user, USER_CACHE_EXPIRE, TimeUnit.MINUTES);
    }
    
    public User getUser(Long userId) {
        String key = USER_KEY_PREFIX + userId;
        return (User) redisTemplate.opsForValue().get(key);
    }
    
    public void deleteUser(Long userId) {
        String key = USER_KEY_PREFIX + userId;
        redisTemplate.delete(key);
    }
}
```

### 4.3 使用@Cacheable注解
```java
@Service
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Cacheable(value = "users", key = "#userId", unless = "#result == null")
    public User getUserById(Long userId) {
        return userRepository.findById(userId).orElse(null);
    }
    
    @CacheEvict(value = "users", key = "#user.id")
    public void updateUser(User user) {
        userRepository.save(user);
    }
    
    @CachePut(value = "users", key = "#result.id")
    public User createUser(User user) {
        return userRepository.save(user);
    }
    
    @CacheEvict(value = "users", key = "#userId")
    public void deleteUser(Long userId) {
        userRepository.deleteById(userId);
    }
}
```

### 4.4 事务支持
```java
@Service
public class TransactionalService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public void performTransactionalOperation() {
        redisTemplate.execute(new SessionCallback<List<Object>>() {
            @Override
            public List<Object> execute(RedisOperations operations) throws DataAccessException {
                operations.multi();
                
                operations.opsForValue().set("key1", "value1");
                operations.opsForValue().set("key2", "value2");
                
                return operations.exec();
            }
        });
    }
}
```

## 5. 常见应用场景实现

### 5.1 用户会话管理
```java
@Service
public class UserSessionService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String SESSION_KEY_PREFIX = "session:";
    private static final String ONLINE_USERS_KEY = "online:users";
    private static final long SESSION_TIMEOUT = 30; // 30分钟
    
    /**
     * 存储用户会话
     */
    public void saveSession(Long userId, User user) {
        String sessionKey = SESSION_KEY_PREFIX + userId;
        // 存储会话信息
        redisTemplate.opsForValue().set(sessionKey, user, SESSION_TIMEOUT, TimeUnit.MINUTES);
        // 添加到在线用户集合
        redisTemplate.opsForSet().add(ONLINE_USERS_KEY, userId);
    }
    
    /**
     * 获取用户会话
     */
    public User getSession(Long userId) {
        String sessionKey = SESSION_KEY_PREFIX + userId;
        // 获取并刷新过期时间
        User user = (User) redisTemplate.opsForValue().get(sessionKey);
        if (user != null) {
            redisTemplate.expire(sessionKey, SESSION_TIMEOUT, TimeUnit.MINUTES);
        }
        return user;
    }
    
    /**
     * 删除用户会话（登出）
     */
    public void removeSession(Long userId) {
        String sessionKey = SESSION_KEY_PREFIX + userId;
        redisTemplate.delete(sessionKey);
        redisTemplate.opsForSet().remove(ONLINE_USERS_KEY, userId);
    }
    
    /**
     * 获取在线用户数
     */
    public Long getOnlineUserCount() {
        return redisTemplate.opsForSet().size(ONLINE_USERS_KEY);
    }
}
```

### 5.2 接口请求限流
```java
@Service
public class RateLimiterService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 简单限流：判断单位时间内请求数是否超过限制
     * @param key 限流标识（如 IP 地址或用户ID）
     * @param limit 单位时间内最大请求数
     * @param timeout 单位时间（秒）
     * @return 是否允许访问
     */
    public boolean isAllowed(String key, int limit, int timeout) {
        String rateLimitKey = "ratelimit:" + key;
        
        // 获取当前计数
        Long count = redisTemplate.opsForValue().increment(rateLimitKey);
        
        // 如果是第一次访问，设置过期时间
        if (count != null && count == 1) {
            redisTemplate.expire(rateLimitKey, timeout, TimeUnit.SECONDS);
        }
        
        // 如果计数小于等于限制数，允许访问
        return count != null && count <= limit;
    }
}
```

### 5.3 分布式锁
```java
@Service
public class DistributedLockService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    /**
     * 尝试获取分布式锁
     * @param lockKey 锁标识
     * @param requestId 请求标识（解锁时用于验证是否为加锁者）
     * @param expireTime 锁过期时间（毫秒）
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey, String requestId, long expireTime) {
        String key = "lock:" + lockKey;
        Boolean result = redisTemplate.opsForValue().setIfAbsent(key, requestId, expireTime, TimeUnit.MILLISECONDS);
        return Boolean.TRUE.equals(result);
    }
    
    /**
     * 释放分布式锁
     * @param lockKey 锁标识
     * @param requestId 请求标识（确保只有加锁者才能解锁）
     * @return 是否成功释放锁
     */
    public boolean releaseLock(String lockKey, String requestId) {
        String key = "lock:" + lockKey;
        
        // 使用Lua脚本，确保操作的原子性
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Long result = redisTemplate.execute(
                new DefaultRedisScript<>(script, Long.class),
                Collections.singletonList(key),
                requestId
        );
        
        return result != null && result == 1L;
    }
}
```

### 5.4 缓存计数器
```java
@Service
public class CounterService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String LIKE_COUNT_KEY = "count:like:";
    private static final String VIEW_COUNT_KEY = "count:view:";
    
    /**
     * 增加点赞计数
     */
    public Long incrementLikeCount(Long postId) {
        String key = LIKE_COUNT_KEY + postId;
        return redisTemplate.opsForValue().increment(key);
    }
    
    /**
     * 减少点赞计数
     */
    public Long decrementLikeCount(Long postId) {
        String key = LIKE_COUNT_KEY + postId;
        return redisTemplate.opsForValue().decrement(key);
    }
    
    /**
     * 获取点赞计数
     */
    public Long getLikeCount(Long postId) {
        String key = LIKE_COUNT_KEY + postId;
        Object count = redisTemplate.opsForValue().get(key);
        return count != null ? Long.parseLong(count.toString()) : 0L;
    }
    
    /**
     * 增加查看计数
     */
    public Long incrementViewCount(Long postId) {
        String key = VIEW_COUNT_KEY + postId;
        return redisTemplate.opsForValue().increment(key);
    }
}
```

### 5.5 排行榜
```java
@Service
public class RankingService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String HOT_POSTS_KEY = "ranking:hot_posts";
    
    /**
     * 更新帖子热度分数
     */
    public void updatePostScore(Long postId, double score) {
        redisTemplate.opsForZSet().add(HOT_POSTS_KEY, postId, score);
    }
    
    /**
     * 增加帖子热度分数
     */
    public void incrementPostScore(Long postId, double increment) {
        redisTemplate.opsForZSet().incrementScore(HOT_POSTS_KEY, postId, increment);
    }
    
    /**
     * 获取热门帖子ID列表（按分数降序）
     */
    public List<Long> getHotPosts(int limit) {
        // 获取分数最高的前limit个帖子
        Set<Object> topPosts = redisTemplate.opsForZSet().reverseRange(HOT_POSTS_KEY, 0, limit - 1);
        
        if (topPosts == null) {
            return new ArrayList<>();
        }
        
        return topPosts.stream()
                .map(id -> Long.parseLong(id.toString()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取帖子排名（从0开始，按分数降序）
     */
    public Long getPostRank(Long postId) {
        return redisTemplate.opsForZSet().reverseRank(HOT_POSTS_KEY, postId);
    }
}
```

## 6. Redis性能优化与注意事项

### 6.1 键命名规范
- 使用冒号分隔不同部分：`对象类型:ID:属性`
- 保持命名一致性：`user:1000:profile`, `user:1000:follows`
- 避免过长的键名，影响性能和内存使用

### 6.2 缓存策略
- **Cache-Aside Pattern**：先更新数据库，再删除缓存
- **Write-Through**：同时更新数据库和缓存
- **Write-Behind**：先更新缓存，异步更新数据库

### 6.3 缓存问题及解决方案
- **缓存穿透**：查询不存在的数据导致请求直达数据库
  - 解决：缓存空值；使用布隆过滤器
- **缓存击穿**：热点key过期导致大量请求直达数据库
  - 解决：互斥锁；设置热点数据永不过期
- **缓存雪崩**：大批量缓存同时过期
  - 解决：随机过期时间；多级缓存

### 6.4 内存优化
- 合理设置过期时间，避免无效数据占用内存
- 使用哈希结构存储对象，节省内存
- 定期清理不必要的数据

### 6.5 连接池配置
- 根据服务器性能和业务需求调整连接池大小
- 设置合理的连接超时时间
- 监控连接池使用情况，避免连接耗尽

## 7. 运维与监控

### 7.1 Redis监控指标
- **内存使用**：`used_memory`, `used_memory_rss`
- **连接数**：`connected_clients`
- **命令执行**：`total_commands_processed`
- **缓存命中率**：`keyspace_hits` / (`keyspace_hits` + `keyspace_misses`)
- **延迟**：可使用专门的延迟监控工具

### 7.2 常用管理命令
```bash
# 查看Redis信息
INFO

# 查看内存使用情况
INFO memory

# 查看客户端连接
CLIENT LIST

# 监控命令执行
MONITOR

# 清空数据库
FLUSHDB

# 保存数据到磁盘
SAVE
```

### 7.3 Spring Boot Actuator集成
在`pom.xml`中添加：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

在`application.properties`中配置：
```properties
# 开启Redis健康检查
management.health.redis.enabled=true
# 暴露健康检查端点
management.endpoints.web.exposure.include=health,info
```

## 8. 参考资源

- [Redis官方文档](https://redis.io/documentation)
- [Spring Data Redis文档](https://docs.spring.io/spring-data/redis/docs/current/reference/html/)
- [Redis命令参考](https://redis.io/commands)
- [Redis设计与实现](http://redisbook.com/)
