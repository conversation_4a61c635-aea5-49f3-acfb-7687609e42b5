package com.djj.campusconnect.mapper;

import com.djj.campusconnect.pojo.User;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface UserMapper {

    @Select("SELECT * FROM user WHERE email = #{email}")
    public User getUserByEmail(String email);

    @Select("SELECT * FROM user WHERE user_id = #{id}")
    public User getUserById(Integer id);

    public Integer createUser(User user);

    public Integer updateUser(User user);
    
    /**
     * 更新用户密码
     * @param email 邮箱
     * @param passwordHash 加密后的密码
     * @return 受影响的行数
     */
    @Update("UPDATE user SET password_hash = #{passwordHash}, updated_at = NOW() WHERE email = #{email}")
    public Integer updatePassword(String email, String passwordHash);
}
