package com.djj.campusconnect.utils;

import com.alibaba.fastjson.JSON;
import com.djj.campusconnect.pojo.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 用户上下文工具类，用于存储和获取当前用户信息
 * 基于ThreadLocal实现，确保线程安全
 */
@Component
public class UserContext {

    // ThreadLocal用于请求处理中快速访问
    private static final ThreadLocal<User> userThreadLocal = new ThreadLocal<>();

    // Redis键前缀
    private static final String USER_INFO_PREFIX = "user:info:";
    private static final String ONLINE_USERS_KEY = "online:users";
    private static final long EXPIRATION = 30 * 60; // 30分钟

    private static StringRedisTemplate redisTemplate;

    @Autowired
    public void setRedisTemplate(StringRedisTemplate redisTemplate) {
        UserContext.redisTemplate = redisTemplate;
    }

    /**
     * 用户登录时调用，将用户信息存入Redis
     */
    public void saveUserToRedis(User user) {
        if (user != null) {
            String userKey = USER_INFO_PREFIX + user.getUserId();
            String userJson = JSON.toJSONString(user);

            // 存储用户信息到Redis
            redisTemplate.opsForValue().set(userKey, userJson, EXPIRATION, TimeUnit.SECONDS);
            // 添加到在线用户集合
            redisTemplate.opsForSet().add(ONLINE_USERS_KEY, user.getUserId().toString());
        }
    }

    /**
     * 拦截器中调用，从Redis获取用户信息并设置到ThreadLocal
     */
    public void setCurrentUser(Integer userId) {
        if (userId != null) {
            // 从Redis获取用户信息
            String userKey = USER_INFO_PREFIX + userId;
            String userJson = redisTemplate.opsForValue().get(userKey);

            if (userJson != null) {
                // 刷新Redis过期时间
                redisTemplate.expire(userKey, EXPIRATION, TimeUnit.SECONDS);
                // 设置到ThreadLocal
                User user = JSON.parseObject(userJson, User.class);
                userThreadLocal.set(user);
            }
        }
    }

    /**
     * 在请求处理过程中快速获取当前用户
     */
    public User getCurrentUser() {
        return userThreadLocal.get();
    }

    /**
     * 获取当前用户ID
     */
    public Integer getCurrentUserId() {
        User user = getCurrentUser();
        return user != null ? user.getUserId() : null;
    }

    /**
     * 请求结束时清理ThreadLocal
     */
    public void remove() {
        userThreadLocal.remove();
    }

    /**
     * 用户登出时调用，清理Redis和ThreadLocal
     */
    public void logout(Integer userId) {
        remove();

        if (userId != null) {
            String userKey = USER_INFO_PREFIX + userId;
            redisTemplate.delete(userKey);
            redisTemplate.opsForSet().remove(ONLINE_USERS_KEY, userId.toString());
        }
    }

    /**
     * 用户信息更新时调用，同步更新Redis缓存
     */
    public void updateUserInRedis(User user) {
        if (user != null) {
            saveUserToRedis(user);
        }
    }

    /**
     * 获取在线用户数量
     */
    public Long getOnlineUserCount() {
        return redisTemplate.opsForSet().size(ONLINE_USERS_KEY);
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(Integer userId) {
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(ONLINE_USERS_KEY, userId.toString()));
    }
}