package com.djj.campusconnect.controller;

import com.djj.campusconnect.pojo.CaptchaVerifyRequest;
import com.djj.campusconnect.pojo.Result;
import com.djj.campusconnect.utils.CaptchaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 验证码控制器，处理图片验证码生成和验证
 */
@Slf4j
@RestController
@RequestMapping("/captcha")
public class CaptchaController {

    @Autowired
    private CaptchaUtils captchaUtils;
    
    /**
     * 生成图片验证码
     * @return 图片验证码Base64编码和验证码ID
     */
    @GetMapping("/generate")
    public Result generateCaptcha() {
        // 生成唯一的验证码ID
        String captchaId = UUID.randomUUID().toString();
        
        // 生成验证码图片
        String captchaBase64 = captchaUtils.generateCaptcha(captchaId);
        
        // 返回验证码ID和图片
        Map<String, String> data = new HashMap<>();
        data.put("captchaId", captchaId);
        data.put("captchaImage", captchaBase64);
        
        log.info("Generated captcha with ID: {}", captchaId);
        return Result.success(data);
    }
    
    /**
     * 验证图片验证码
     * @param request 验证码验证请求
     * @return 验证结果
     */
    @PostMapping("/verify")
    public Result verifyCaptcha(@RequestBody CaptchaVerifyRequest request) {
        log.info("Verifying captcha with ID: {}", request.getCaptchaId());
        boolean isValid = captchaUtils.verifyCaptcha(request.getCaptchaId(), request.getCode());
        
        if (isValid) {
            return Result.success("验证成功");
        } else {
            return Result.error(400, "验证码错误或已过期");
        }
    }
}
