package com.djj.campusconnect.mapper;

import com.djj.campusconnect.pojo.PostMedia;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PostMediaMapper {

    @Insert("INSERT INTO post_media (post_id, media_type, sort_order, media_url, created_at)" +
            " VALUES (#{postId}, #{mediaType}, #{sortOrder}, #{mediaUrl}, NOW())")
    public Integer insert(PostMedia postMedia);

    @Select("SELECT * FROM post_media WHERE post_id = #{postId}")
    public List<PostMedia> selectByPostId(Integer postId);

}
