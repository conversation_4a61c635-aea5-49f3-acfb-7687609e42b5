##### 5.3.1.2 t_post_mediau8868uff08u52a8u6001u5a92u4f53u8868uff09

```sql
CREATE TABLE `t_post_media` (
  `media_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u5a92u4f53ID',
  `post_id` bigint(20) NOT NULL COMMENT 'u52a8u6001ID',
  `media_url` varchar(255) NOT NULL COMMENT 'u5a92u4f53URL',
  `media_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'u5a92u4f53u7c7bu578buff0c1=u56feu7247uff0c2=u89c6u9891',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT 'u6392u5e8fu53f7',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  PRIMARY KEY (`media_id`),
  KEY `idx_post_id` (`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u52a8u6001u5a92u4f53u8868';
```

##### 5.3.1.3 t_postu_tagu8868uff08u52a8u6001u6807u7b7eu5173u8054u8868uff09

```sql
CREATE TABLE `t_post_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u5173u8054ID',
  `post_id` bigint(20) NOT NULL COMMENT 'u52a8u6001ID',
  `tag_id` bigint(20) NOT NULL COMMENT 'u6807u7b7eID',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_tag` (`post_id`,`tag_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u52a8u6001u6807u7b7eu5173u8054u8868';
```

##### 5.3.1.4 t_tagu8868uff08u6807u7b7eu8868uff09

```sql
CREATE TABLE `t_tag` (
  `tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u6807u7b7eID',
  `name` varchar(50) NOT NULL COMMENT 'u6807u7b7eu540du79f0',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'u6807u7b7eu7c7bu578buff0c1=u7cfbu7edfu9884u8bbeff0c2=u7528u6237u81eau5b9au4e49',
  `hot_count` int(11) NOT NULL DEFAULT '0' COMMENT 'u6807u7b7eu70edu5ea6u6570',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'u72b6u6001uff0c1=u6b63u5e38uff0c0=u5df2u5220u9664',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  `updated_at` datetime NOT NULL COMMENT 'u66f4u65b0u65f6u95f4',
  PRIMARY KEY (`tag_id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u6807u7b7eu8868';
```

##### 5.3.1.5 t_user_interest_tagu8868uff08u7528u6237u5174u8da3u6807u7b7eu8868uff09

```sql
CREATE TABLE `t_user_interest_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u8bb0u5f55ID',
  `user_id` bigint(20) NOT NULL COMMENT 'u7528u6237ID',
  `tag_id` bigint(20) NOT NULL COMMENT 'u6807u7b7eID',
  `score` int(11) NOT NULL DEFAULT '10' COMMENT 'u5174u8da3u5206u6570',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  `updated_at` datetime NOT NULL COMMENT 'u66f4u65b0u65f6u95f4',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_tag` (`user_id`,`tag_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u7528u6237u5174u8da3u6807u7b7eu8868';
```

##### 5.3.1.6 t_commentu8868uff08u8bc4u8bbau8868uff09

```sql
CREATE TABLE `t_comment` (
  `comment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u8bc4u8bbaID',
  `post_id` bigint(20) NOT NULL COMMENT 'u52a8u6001ID',
  `user_id` bigint(20) NOT NULL COMMENT 'u7528u6237ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT 'u7236u8bc4u8bbaID',
  `reply_to_user_id` bigint(20) DEFAULT NULL COMMENT 'u56deu590du7684u7528u6237ID',
  `content` varchar(500) NOT NULL COMMENT 'u8bc4u8bbau5185u5bb9',
  `like_count` int(11) NOT NULL DEFAULT '0' COMMENT 'u70b9u8d5eu6570',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'u72b6u6001uff0c1=u6b63u5e38uff0c0=u5df2u5220u9664',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  `updated_at` datetime NOT NULL COMMENT 'u66f4u65b0u65f6u95f4',
  PRIMARY KEY (`comment_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u8bc4u8bbau8868';
```

##### 5.3.1.7 t_post_likeu8868uff08u52a8u6001u70b9u8d5eu8868uff09

```sql
CREATE TABLE `t_post_like` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u8bb0u5f55ID',
  `post_id` bigint(20) NOT NULL COMMENT 'u52a8u6001ID',
  `user_id` bigint(20) NOT NULL COMMENT 'u7528u6237ID',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_user` (`post_id`,`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u52a8u6001u70b9u8d5eu8868';
```

##### 5.3.1.8 t_comment_likeu8868uff08u8bc4u8bbau70b9u8d5eu8868uff09

```sql
CREATE TABLE `t_comment_like` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u8bb0u5f55ID',
  `comment_id` bigint(20) NOT NULL COMMENT 'u8bc4u8bbaID',
  `user_id` bigint(20) NOT NULL COMMENT 'u7528u6237ID',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`,`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u8bc4u8bbau70b9u8d5eu8868';
```

##### 5.3.1.9 t_post_shareu8868uff08u52a8u6001u5206u4eabu8868uff09

```sql
CREATE TABLE `t_post_share` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u8bb0u5f55ID',
  `post_id` bigint(20) NOT NULL COMMENT 'u52a8u6001ID',
  `user_id` bigint(20) NOT NULL COMMENT 'u5206u4eabu7528u6237ID',
  `shared_to_user_id` bigint(20) NOT NULL COMMENT 'u88abu5206u4eabu7528u6237ID',
  `message` varchar(200) DEFAULT NULL COMMENT 'u5206u4eabu9644u8a00',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_shared_to_user_id` (`shared_to_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u52a8u6001u5206u4eabu8868';
```

##### 5.3.1.10 t_user_view_historyu8868uff08u7528u6237u6d4fu89c8u5386u53f2u8868uff09

```sql
CREATE TABLE `t_user_view_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'u8bb0u5f55ID',
  `user_id` bigint(20) NOT NULL COMMENT 'u7528u6237ID',
  `post_id` bigint(20) NOT NULL COMMENT 'u52a8u6001ID',
  `view_time` datetime NOT NULL COMMENT 'u6d4fu89c8u65f6u95f4',
  `view_duration` int(11) NOT NULL DEFAULT '0' COMMENT 'u6d4fu89c8u65f6u957f(u79d2)',
  `created_at` datetime NOT NULL COMMENT 'u521bu5efau65f6u95f4',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_view_time` (`view_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='u7528u6237u6d4fu89c8u5386u53f2u8868';
```

### 5.4 u7f13u5b58u8bbeu8ba1

#### 5.4.1 Redisu7f13u5b58u8bbeu8ba1

##### 5.4.1.1 u70edu69cbu6392u884cu7f13u5b58

| u7f13u5b58u952e | u7c7bu578b | u63cfu8ff0 | u8fc7u671fu7b56u7565 |
| --- | --- | --- | --- |
| `ranking:hot_posts` | Sorted Set | u70edu95e8u52a8u6001u6392u884cu69cbu5b58u50a8ID-u70edu5ea6u5206u6570u952eu503cu5bf9 | 24u5c0fu65f6 |
| `ranking:latest_posts` | Sorted Set | u6700u65b0u52a8u6001u6392u884cu69cbu5b58u50a8ID-u65f6u95f4u6233u952eu503cu5bf9 | 24u5c0fu65f6 |
| `tag:posts:{tagId}` | Sorted Set | u5404u6807u7b7eu76f8u5173u52a8u6001u96c6u5408 | 72u5c0fu65f6 |

##### 5.4.1.2 u52a8u6001u8ba1u6570u7f13u5b58

| u7f13u5b58u952e | u7c7bu578b | u63cfu8ff0 | u8fc7u671fu7b56u7565 |
| --- | --- | --- | --- |
| `post:like_count:{postId}` | String | u52a8u6001u70b9u8d5eu6570 | u65e0u8fc7u671f |
| `post:comment_count:{postId}` | String | u52a8u6001u8bc4u8bbau6570 | u65e0u8fc7u671f |
| `post:view_count:{postId}` | String | u52a8u6001u6d4fu89c8u6570 | u65e0u8fc7u671f |
| `post:share_count:{postId}` | String | u52a8u6001u5206u4eabu6570 | u65e0u8fc7u671f |

##### 5.4.1.3 u7528u6237u4ea4u4e92u7f13u5b58

| u7f13u5b58u952e | u7c7bu578b | u63cfu8ff0 | u8fc7u671fu7b56u7565 |
| --- | --- | --- | --- |
| `user:liked:{userId}` | Set | u7528u6237u5df2u70b9u8d5eu52a8u6001u96c6u5408 | 30u5929 |
| `user:read:{userId}` | Set | u7528u6237u5df2u8bbfu95eeu52a8u6001u96c6u5408 | 30u5929 |
| `user:disinterest:{userId}` | Set | u7528u6237u4e0du611fu5174u8da3u52a8u6001u96c6u5408 | 90u5929 |

##### 5.4.1.4 u63a8u8350u7f13u5b58

| u7f13u5b58u952e | u7c7bu578b | u63cfu8ff0 | u8fc7u671fu7b56u7565 |
| --- | --- | --- | --- |
| `recommendation:{userId}` | Sorted Set | u7528u6237u63a8u8350u52a8u6001u96c6u5408 | 4u5c0fu65f6 |
| `feed:friend:{userId}` | Sorted Set | u7528u6237u597du53cbu52a8u6001u96c6u5408 | 24u5c0fu65f6 |

### 5.5 u6027u80fdu4f18u5316u7b56u7565

#### 5.5.1 u7d22u5f15u4f18u5316

1. u5bf9u9ad8u9891u67e5u8be2u5b57u6bb5u8bbeu7f6eu7d22u5f15uff0cu5305u62ecu7528u6237IDu3001u6807u7b7eIDu3001u70edu5ea6u5206u6570u7b49
2. u5bf9u8fde
u63a5u67e5u8be2u7684u5916u952eu5b57u6bb5u8bbeu7f6eu7d22u5f15uff0cu5982user_idu3001post_idu3001tag_id
3. u5bf9u8054u5408u67e5u8be2u5b57u6bb5u8bbeu7f6eu7ec4u5408u7d22u5f15uff0cu5982(post_id, user_id)

#### 5.5.2 u7f13u5b58u4f18u5316

1. u4f7fu7528Redisu7f13u5b58u70edu95e8u52a8u6001u6392u884cu6570u636euff0cu51cfu5c11u6570u636eu5e93u538bu529b
2. u4f7fu7528u7f13u5b58u5b58u50a8u52a8u6001u8ba1u6570u6570u636euff0cu51cfu5c11u9891u7e41u5199u5165u6570u636eu5e93
3. u5b9au671fu5c06u7f13u5b58u6570u636eu6301u4e45u5316u5230u6570u636eu5e93uff0cu9632u6b62u7f13u5b58u6570u636eu4e22u5931
4. u4f7fu7528u5ef6u8fddu52a0u8f7du6a21u5f0fu51cfu5c11u4e0du5fc5u8981u7684u6570u636eu52a0u8f7d

#### 5.5.3 u5206u9875u4f18u5312

1. u4f7fu7528PageHelperu5b9eu73b0u9ad8u6548u5206u9875uff0cu907fu514du5168u8868u626bu63cf
2. u63a8u8350u9875u9762u91c7u7528u6ed1u52a8u5206u9875u6a21u5f0fuff0cu63d0u9ad8u7528u6237u4f53u9a8c
3. u63a8u8350u7ed3u679cu9884u52a0u8f7duff0cu63d0u524du8ba1u7b97u4e0bu4e00u9875u5185u5bb9

#### 5.5.4 u5e76u53d1u4f18u5316

1. u70edu70b9u6570u636eu66f4u65b0u91c7u7528u5f02u6b65u64cdu4f5cuff0cu907fu514du963bu585eu4e3bu6d41u7a0b
2. u63a8u8350u7ed3u679cu8ba1u7b97u91c7u7528u5b9au65f6u4efbu52a1u65b9u5f0fuff0cu907fu514du5b9eu65f6u8ba1u7b97
3. u6570u636eu7edf
u8ba1u4efbu52a1u901au8fc7u5b9au65f6u4efbu52a1u65b9u5f0fu6267u884cuff0cu907fu514du5f71u54cdu4e3bu4e1au52a1

### 5.6 u5b89u5168u6027u8bbeu8ba1

#### 5.6.1 u6570u636eu53d6u51fa
u6743u9650u63a7u5236

1. u6240u6709APIu63a5u53e3u5fc5u987bu8fdb
u884cu7528u6237u9a8cu8bc1uff0cu9a8cu8bc1u7528u6237u662fu5426u767bu5f55
2. u7b26u5408u53efu89c1u6027u8bbeu8ba1uff0cu79c1u5bc6u52a8u6001u53eau4f1au5c55u793au7ed9u7528u6237u81eau8eabu6216u597du53cb
3. u654fu611fu64cdu4f5cuff08u5220u9664u3001u4feeu6539uff09u5fc5u987bu9a8cu8bc1u7528u6237u662fu5426u6709u6743u9650

#### 5.6.2 u5185u5bb9u5b89u5168

1. u7528u6237u53d1u5e03u7684u5185u5bb9u9700u8fdb
u884cu654fu611fu8bceu8bbeu5904u7406
2. u56feu7247u3001u89c6u9891u5185u5bb9u5b9eu65f6u5ba1u6838uff0cu9632u6b62u8fdd
u89c4u5185u5bb9u4f20u64ad
3. u7528u6237u4e3eu62a5u673au5236uff0cu5141u8bb8u5176u4ed6u7528u6237u4e3eu62a5u4e0du826fu5185u5bb9

#### 5.6.3 u9632u8214u7b56u7565

1. u9650u5236u7528u6237u53d1u5e03u5185u5bb9u9891u7387uff0cu9632u6b62u5783u573eu5185u5bb9u6cc4u6d2a
2. u8bc4u8bbau9650u5236u9891u7387uff0cu9632u6b62u6076u610fu8bc4u8bba
3. u5185u5bb9u8fc7u6ee4u673au5236uff0cu81eau52a8u8fc7u6ee4u654fu611fu8bceu8bbeu53cau5e7fu544au4fe1u606f

## 6. u603bu7ed3

u672cu8bbeu8ba1u6587u6863u8be6u7ec6u63cfu8ff0u4e86u6821u56eduebbcu63a5u5e73u53f0u52a8u6001u6a21u5757u7684u8bbeu8ba1uff0cu5305u62ecu529fu80fdu9700u6c42u3001u63a5u53e3u8bbeu8ba1u3001u6570u636eu6a21u578bu8bbeu8ba1u53cau7f13u5b58u8bbeu8ba1u7b49u3002u901au8fc7u5927u5385u3001u63a8u8350u3001u597du53cbu52a8u6001u4e09u4e2au5b50u6a21u5757u7684u8bbeu8ba1uff0cu5b9eu73b0u4e86u7528u6237u4e4bu95f4u7684u4e92u52a8u3001u4fe1u606fu5206u4eabu548cu4e2au6027u5316u63a8u8350u3002

u4e0bu4e00u6b65u5c06u6839u636eu672cu8bbeu8ba1u6587u6863u8fdb
u884cu5177u4f53u7684u7f16u7801u5b9eu73b0uff0cu5e76u8fdb
u884cu6027u80fdu6d4bu8bd5u548cu5b89u5168u6d4bu8bd5uff0cu4ee5u786eu4fddu6a21u5757u7684u8d28u91cfu548cu53d7u6b27u63a5u5e94u7b26u5408u8981u6c42u3002

u901au8fc7u7f13u5b58u8bbeu8ba1u548cu6027u80fdu4f18u5316u7b56u7565uff0cu4fdd
u8bc1u4e86u5728u5927u91cfu7528u6237u540cu65f6u4f7fu7528u65f6u7684u7cfbu7edfu6027u80fdu548cu7a33u5b9au6027u3002u540cu65f6uff0cu5b89u5168u6027u8bbeu8ba1u4fdd
u8bc1u4e86u7528u6237u6570u636eu548cu5185u5bb9u7684u5b89u5168u6027uff0cu9632u6b62u6076u610fu5185u5bb9u4f20u64adu548cu4e0du826fu884cu4e3au3002
