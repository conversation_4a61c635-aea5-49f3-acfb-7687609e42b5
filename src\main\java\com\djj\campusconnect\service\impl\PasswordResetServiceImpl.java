package com.djj.campusconnect.service.impl;

import com.djj.campusconnect.mapper.UserMapper;
import com.djj.campusconnect.pojo.ResetPasswordRequest;
import com.djj.campusconnect.pojo.User;
import com.djj.campusconnect.pojo.VerifyCodeRequest;
import com.djj.campusconnect.utils.EmailUtils;
import com.djj.campusconnect.service.PasswordResetService;
import com.djj.campusconnect.utils.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PasswordResetServiceImpl implements PasswordResetService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private EmailUtils emailUtils;

    @Autowired
    private UserContext userContext;

    /**
     * 发送密码重置验证码
     *
     * @param email 用户邮箱
     * @return 成功发送返回true，失败返回false
     */
    @Override
    public Boolean sendPasswordResetCode(String email) {
        try {
            // 验证邮箱是否存在
            User user = userMapper.getUserByEmail(email);
            if (user == null) {
                return false;
            }
            
            // 发送验证码
            emailUtils.sendVerificationCode(email);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 验证邮箱验证码
     *
     * @param request 包含邮箱和验证码的请求
     * @return 验证成功返回true，失败返回false
     */
    @Override
    public Boolean verifyEmailCode(VerifyCodeRequest request) {
        try {
            // 验证邮箱是否存在
            User user = userMapper.getUserByEmail(request.getEmail());
            if (user == null) {
                return false;
            }
            
            // 验证验证码
            return emailUtils.verifyCode(request.getEmail(), request.getCode());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 重置密码
     *
     * @param request 包含邮箱、验证码和新密码的请求
     * @return 重置成功返回true，失败返回false
     */
    @Override
    public Boolean resetPassword(ResetPasswordRequest request) {
        try {
            // 验证邮箱是否存在
            User user = userMapper.getUserByEmail(request.getEmail());
            if (user == null) {
                log.info("用户不存在:"+ request.getEmail());
                return false;
            }
            
            // 验证验证码
            boolean isCodeValid = emailUtils.verifyCode(request.getEmail(), request.getCode());
            if (!isCodeValid) {
                log.info("验证码错误:" + request.getCode());
                return false;
            }
            
            // 更新密码
            String passwordHash = DigestUtils.md5Hex(request.getNewPassword());
            Integer result = userMapper.updatePassword(request.getEmail(), passwordHash);
            
            // 更新Redis缓存中的用户信息
            if (result > 0) {
                userContext.updateUserInRedis(user);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
