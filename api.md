# Campus Connect API 文档

## 目录

- [1. 概述](#1-概述)
- [2. 通用响应格式](#2-通用响应格式)
- [3. 用户模块](#3-用户模块)
  - [3.1 用户注册](#31-用户注册)
  - [3.2 用户登录](#32-用户登录)
  - [3.3 更新用户信息](#33-更新用户信息)
  - [3.4 获取用户个人资料](#34-获取用户个人资料)
- [4. 密码重置模块](#4-密码重置模块)
  - [4.1 请求密码重置验证码](#41-请求密码重置验证码)
  - [4.2 验证邮箱验证码](#42-验证邮箱验证码)
  - [4.3 重置密码](#43-重置密码)
- [5. 图片验证码模块](#5-图片验证码模块)
  - [5.1 生成图片验证码](#51-生成图片验证码)
  - [5.2 验证图片验证码](#52-验证图片验证码)
- [6. 文件上传](#6-文件上传)
  - [6.1 上传图片/视频](#61-上传图片视频)
- [7. 动态模块](#7-动态模块)
  - [7.1 获取所有动态](#71-获取所有动态)
  - [7.2 获取热门动态](#72-获取热门动态)
  - [7.3 获取最新动态](#73-获取最新动态)
  - [7.4 发布动态](#74-发布动态)
  - [7.5 上传动态媒体文件](#75-上传动态媒体文件)
- [8. 标签模块](#8-标签模块)
  - [8.1 获取所有标签](#81-获取所有标签)
  - [8.2 创建标签](#82-创建标签)
- [9. 数据模型](#9-数据模型)
  - [9.1 用户模型](#91-用户模型)

## 1. 概述

Campus Connect API 是一个面向校园社交平台的RESTful API接口。所有API都返回JSON格式数据，并采用标准HTTP状态码表示请求结果。

- 基础URL: `http://localhost:8080`
- 认证方式: JWT令牌，在请求头中以`token`字段传递

## 2. 通用响应格式

所有API返回统一的JSON格式:

```json
{
  "code": 200,          // 状态码，200表示成功，其他表示失败
  "message": "success", // 响应消息
  "data": {}            // 响应数据，可能是对象、数组或null
}
```

### 常见状态码

| 状态码 | 描述 |
| ------ | ---- |
| 200    | 请求成功 |
| 400    | 请求参数错误 |
| 401    | 未认证，需要登录 |
| 403    | 权限不足 |
| 404    | 资源不存在 |
| 500    | 服务器内部错误 |

## 3. 用户模块

### 3.1 用户注册

注册新用户账号。注册需要先获取邮箱验证码。

**请求URL**: `/user/register`

**请求方式**: `POST`

**请求参数**:

请求包含两个实体：User对象和VerifyCodeRequest对象

**User对象参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| email | String | 是 | 用户邮箱（唯一） |
| passwordHash | String | 是 | 用户密码(这里前端填明文密码即可) |
| nickname | String | 是 | 用户昵称（唯一） |
| avatarUrl | String | 否 | 用户头像URL |
| department | String | 否 | 所属院系 |
| school | String | 否 | 所属学校 |
| beforeCardUrl | String | 否 | 校园卡正面照片URL |
| afterCardUrl | String | 否 | 校园卡反面照片URL |

**VerifyCodeRequest对象参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| email | String | 是 | 用户邮箱 |
| code | String | 是 | 邮箱验证码 |

**请求示例**:

```json
// 注意：这里包含两个实体对象，在实际请求中需要根据前端框架正确处理多实体参数
{
  "user": {
    "email": "<EMAIL>",
    "nickname": "Campus_User",
    "passwordHash": "yourpasswordhash"
  },
  "request": {
    "email": "<EMAIL>",
    "code": "123456"
  }
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "注册成功",
  "data": null
}
```

**错误返回**:

```json
{
  "code": 400,
  "message": "验证码错误或已过期",
  "data": null
}
```

**注意事项**:

这里的四个注册字段：学校、院系、校园卡正面照片URL和反面照片URL在注册页面是不必渲染的，因为根据业务逻辑，后面应该有一个专门的认证页面来上传校园卡的这两张图片，并且等到管理员审核通过之后，服务器端会自动识别并填写学校和院系这两个字段。

注册前需要先使用 `/user/sendCode` 接口获取邮箱验证码。

### 3.2 用户登录

用户登录获取认证令牌。

**请求URL**: `/user/login`

**请求方式**: `POST`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| email | String | 是 | 用户邮箱 |
| passwordHash | String | 是 | 用户密码（这里前端填明文密码即可） |

**请求示例**:

```json
{
  "email": "<EMAIL>",
  "passwordHash": "yourpasswordhash"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "userId": 1,
      "email": "<EMAIL>",
      "nickname": "Campus_User",
      "avatarUrl": "https://example.com/avatar.jpg",
      "authStatus": 0,
      "accountStatus": 0,
      "department": "计算机科学与技术",
      "school": "某大学",
      "createTime": "2025-05-20T10:00:00",
      "updateTime": "2025-05-20T10:00:00"
    }
  }
}
```

### 3.3 更新用户信息

更新当前登录用户的个人信息。

**请求URL**: `/user/update`

**请求方式**: `POST`

**请求头**:
- `token`:  JWT令牌

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| userId | Integer | 是 | 用户ID |
| nickname | String | 否 | 用户昵称 |
| avatarUrl | String | 否 | 用户头像URL |
| department | String | 否 | 所属院系 |
| school | String | 否 | 所属学校 |
| beforeCardUrl | String | 否 | 校园卡正面照片URL |
| afterCardUrl | String | 否 | 校园卡反面照片URL |

**请求示例**:

```json
{
  "userId": 1,
  "nickname": "New_Nickname",
  "department": "电子工程学院"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

### 3.4 获取用户个人资料

获取当前登录用户的个人资料。

**请求URL**: `/user/profile`

**请求方式**: `GET`

**请求头**:
- `token`:  JWT令牌

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 1,
    "email": "<EMAIL>",
    "nickname": "Campus_User",
    "avatarUrl": "https://example.com/avatar.jpg",
    "authStatus": 0,
    "accountStatus": 0,
    "department": "计算机科学与技术",
    "school": "某大学",
    "createTime": "2025-05-20T10:00:00",
    "updateTime": "2025-05-20T10:00:00"
  }
}
```

### 3.5 发送注册验证码

向用户邮箱发送注册验证码。

**请求URL**: `/user/sendCode`

**请求方式**: `POST`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| email | String | 是 | 用户邮箱 |

**请求示例**:

```json
{
  "email": "<EMAIL>"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "验证码已发送到您的邮箱",
  "data": null
}
```

## 4. 密码重置模块

### 4.1 请求密码重置验证码

向用户邮箱发送密码重置验证码。

**请求URL**: `/password/forgot`

**请求方式**: `POST`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| email | String | 是 | 用户邮箱 |

**请求示例**:

```json
{
  "email": "<EMAIL>"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "验证码已发送到您的邮箱",
  "data": null
}
```

### 4.2 验证邮箱验证码

验证用户提交的邮箱验证码是否有效。

**请求URL**: `/password/verify`

**请求方式**: `POST`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| email | String | 是 | 用户邮箱 |
| code | String | 是 | 验证码 |

**请求示例**:

```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "验证码正确",
  "data": null
}
```

### 4.3 重置密码

使用验证码重置用户密码。

**请求URL**: `/password/reset`

**请求方式**: `POST`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| email | String | 是 | 用户邮箱 |
| code | String | 是 | 验证码 |
| newPassword | String | 是 | 新密码 |

**请求示例**:

```json
{
  "email": "<EMAIL>",
  "code": "123456",
  "newPassword": "newpassword123"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": null
}
```

## 5. 图片验证码模块

### 5.1 生成图片验证码

生成图片验证码，返回验证码ID和图片Base64编码。

**请求URL**: `/captcha/generate`

**请求方式**: `GET`

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "captchaId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "captchaImage": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...(省略部分内容)"
  }
}
```

### 5.2 验证图片验证码

验证用户输入的图片验证码。

**请求URL**: `/captcha/verify`

**请求方式**: `POST`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| captchaId | String | 是 | 验证码ID |
| code | String | 是 | 用户输入的验证码 |

**请求示例**:

```json
{
  "captchaId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "code": "ABC123"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "验证成功",
  "data": null
}
```

## 6. 文件上传

### 6.1 上传图片/视频

上传图片或视频文件。

**请求URL**: `/upload`

**请求方式**: `POST`

**Content-Type**: `multipart/form-data`

**请求头**:
- `token`:  JWT令牌

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| file | File | 是 | 要上传的文件 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": "https://example-cos.com/path/to/file.jpg"
}
```

## 7. 动态模块

### 7.1 获取所有动态

获取所有动态列表。

**请求URL**: `/post/all`

**请求方式**: `GET`

**请求头**:
- `token`:  JWT令牌（可选，未登录用户也可访问）

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "postId": 1,
      "userId": 101,
      "content": "这是一条动态内容",
      "hotScore": 85.6,
      "likeNum": 42,
      "commentNum": 18,
      "viewNum": 356,
      "visibility": 0,
      "createdAt": "2025-05-20T14:30:45",
      "updatedAt": "2025-05-20T14:30:45"
    },
    // 更多动态...
  ]
}
```

### 7.2 获取热门动态

获取热门动态列表，按热度排序。

**请求URL**: `/post/hot`

**请求方式**: `GET`

**请求头**:
- `token`:  JWT令牌（可选，未登录用户也可访问）

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| start | Integer | 否 | 起始位置，默认为0 |
| PostNum | Integer | 否 | 获取条数，默认为15 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [1, 5, 12, 8, 24]  // 返回热门动态的ID列表
}
```

### 7.3 获取最新动态

获取最新动态列表，按发布时间排序。

**请求URL**: `/post/new`

**请求方式**: `GET`

**请求头**:
- `token`:  JWT令牌（可选，未登录用户也可访问）

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| start | Integer | 否 | 起始位置，默认为0 |
| PostNum | Integer | 否 | 获取条数，默认为15 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [30, 29, 28, 27, 26]  // 返回最新动态的ID列表
}
```

### 7.4 发布动态

发布新动态。

**请求URL**: `/post/create`

**请求方式**: `POST`

**请求头**:
- `token`:  JWT令牌

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| content | String | 是 | 动态内容 |
| visibility | Integer | 否 | 可见性(0:公开,1:好友可见,2:仅自己可见)，默认为0 |

**请求示例**:

```json
{
  "content": "这是我的第一条动态！",
  "visibility": 0
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "帖子创建成功",
  "data": null
}
```

### 7.5 上传动态媒体文件

为动态上传媒体文件（图片/视频）。

**请求URL**: `/post/media/upload`

**请求方式**: `POST`

**Content-Type**: `multipart/form-data`

**请求头**:
- `token`:  JWT令牌

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| file | File | 是 | 要上传的媒体文件 |
| postId | Integer | 是 | 关联的动态ID |
| mediaType | Integer | 是 | 媒体类型(0:图片,1:视频) |
| sortOrder | Integer | 否 | 排序顺序，默认为0 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": "https://example-cos.com/path/to/media.jpg"
}
```

### 7.6 获取动态媒体文件

获取特定动态的媒体文件列表。

**请求URL**: `/post/media/getPostMedia`

**请求方式**: `GET`

**请求头**:
- `token`:  JWT令牌（可选，未登录用户也可访问）

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| postId | Integer | 是 | 动态ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "mediaId": 1,
      "postId": 101,
      "mediaUrl": "https://example-cos.com/path/to/image1.jpg",
      "mediaType": 0,
      "sortOrder": 0,
      "createdAt": "2025-05-24T10:30:00"
    },
    {
      "mediaId": 2,
      "postId": 101,
      "mediaUrl": "https://example-cos.com/path/to/image2.jpg",
      "mediaType": 0,
      "sortOrder": 1,
      "createdAt": "2025-05-24T10:30:15"
    }
  ]
}
```

## 8. 标签模块

### 8.1 获取所有标签

获取系统中的所有标签。

**请求URL**: `/tag/all`

**请求方式**: `GET`

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "tagId": 1,
      "name": "校园活动",
      "createdAt": "2025-05-01T10:00:00"
    },
    {
      "tagId": 2,
      "name": "学习资料",
      "createdAt": "2025-05-01T10:05:00"
    },
    // 更多标签...
  ]
}
```

### 8.2 创建标签

创建新标签。

**请求URL**: `/tag/create`

**请求方式**: `POST`

**请求头**:
- `token`:  JWT令牌

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| name | String | 是 | 标签名称 |

**请求示例**:

```json
{
  "name": "期末考试"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tagId": 10,
    "name": "期末考试",
    "createdAt": "2025-05-24T14:42:00"
  }
}
```

## 9. 数据模型

### 9.1 用户模型

用户表(`user`)存储用户基本信息。

| 字段名 | 类型 | 必填 | 默认值 | 描述 |
| ------ | ---- | ---- | ------ | ---- |
| user_id | BIGINT | 自动生成 | 自增 | 用户ID，主键 |
| email | VARCHAR(25) | 是 | 无 | 用户邮箱，唯一 |
| password_hash | VARCHAR(128) | 是 | 无 | 密码哈希值 |
| nickname | VARCHAR(50) | 是 | 无 | 用户昵称，唯一 |
| avatar_url | VARCHAR(255) | 否 | NULL | 用户头像URL |
| auth_status | TINYINT | 否 | 0 | 认证状态(0:未认证,1:已认证,2:认证中) |
| account_status | TINYINT | 否 | 0 | 账号状态(0:正常,1:禁用) |
| department | VARCHAR(50) | 否 | NULL | 所属院系 |
| school | VARCHAR(20) | 否 | NULL | 所属学校 |
| before_card_url | VARCHAR(255) | 否 | NULL | 校园卡正面图片URL |
| after_card_url | VARCHAR(255) | 否 | NULL | 校园卡反面图片URL |
| created_at | DATETIME | 自动生成 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | 自动生成 | CURRENT_TIMESTAMP | 更新时间 |

**注意事项**:

1. **必填字段**:
   - `email`: 用户邮箱，必须唯一
   - `password_hash`: 密码哈希，前端需要进行加密处理
   - `nickname`: 用户昵称，必须唯一

2. **可选字段**:
   - `avatar_url`: 用户头像URL，可通过文件上传接口获取
   - `department`: 用户所属院系
   - `school`: 用户所属学校
   - `before_card_url`和`after_card_url`: 校园卡照片，用于实名认证

3. **自动生成字段**:
   - `user_id`: 系统自动生成的用户ID
   - `auth_status`: 默认为0(未认证)
   - `account_status`: 默认为0(正常)
   - `created_at`和`updated_at`: 系统自动维护的时间戳
