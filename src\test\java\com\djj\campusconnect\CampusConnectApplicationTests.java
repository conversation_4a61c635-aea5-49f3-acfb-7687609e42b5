package com.djj.campusconnect;

import com.djj.campusconnect.utils.OCRUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

// 使用普通的JUnit测试，不加载Spring上下文
@ExtendWith(MockitoExtension.class)
class OCRUtilsTest {

    // 直接创建实例，不使用Spring注入
    private OCRUtils ocrUtils;
    
    @BeforeEach
    void setUp() {
        // 手动创建OCRUtils实例
        ocrUtils = new OCRUtils();
    }

    @Test
    void testOCR() {
        // 使用本地测试图片路径
        String imagePath = "https://social-platform-1349546598.cos.ap-guangzhou.myqcloud.com/image/before.jpg";
        try {
            // 直接调用方法进行测试
            String result = ocrUtils.performOCR(imagePath, 1);
            System.out.println("OCR结果: " + result);
            // 这里可以添加断言来验证结果
        } catch (Exception e) {
            System.out.println("OCR测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
