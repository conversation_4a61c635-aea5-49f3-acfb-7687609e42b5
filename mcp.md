# MCP (Mock Content Population) 工具实现指南

## 1. 概述

MCP是一个用于自动生成校园社交平台测试数据的工具，主要解决开发和测试过程中数据不足的问题。本文档提供具体的实现方法和可行方案，而非抽象设计。

## 2. 快速实现方案

### 2.1 技术栈选择

选择简单易用的技术栈，避免过度工程化：

- **语言**: Python 3.9+（易于快速开发）
- **HTTP客户端**: Requests（调用目标系统API）
- **内容生成**: OpenAI API（生成真实文本内容）
- **数据存储**: SQLite（轻量级，无需额外配置）
- **调度**: APScheduler（简单的任务调度）
- **图片处理**: Pillow（基本图片处理）

### 2.2 项目结构

```
mcp/
├── config.py           # 配置文件
├── main.py             # 入口文件
├── db.py               # 数据库操作
├── api_client.py       # 目标系统API客户端
├── generators/
│   ├── __init__.py
│   ├── account.py      # 账号生成器
│   ├── content.py      # 内容生成器
│   ├── behavior.py     # 行为生成器
│   └── utils.py        # 工具函数
├── resources/
│   ├── avatars/        # 头像图片
│   ├── images/         # 内容图片
│   └── templates/      # 内容模板
└── data/
    └── mcp.db          # SQLite数据库
```

## 3. 具体实现方法

### 3.1 账号生成

```python
# generators/account.py
import random
import string
import requests
from PIL import Image
from io import BytesIO

from config import API_BASE_URL, AVATAR_DIR
import api_client
import db

# 常用中文姓氏
SURNAMES = ["李", "王", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴"]

# 常用英文名
ENGLISH_NAMES = ["Alex", "Emma", "Jack", "Lily", "Max", "Sophia", "Tom", "Zoe"]

def generate_nickname():
    """生成随机昵称"""
    if random.choice([True, False]):
        # 中文昵称: 姓氏 + 随机2个汉字
        surname = random.choice(SURNAMES)
        # 常用汉字Unicode范围
        chars = [chr(random.randint(0x4e00, 0x9fff)) for _ in range(2)]
        return surname + ''.join(chars)
    else:
        # 英文昵称: 英文名 + 随机数字
        name = random.choice(ENGLISH_NAMES)
        number = ''.join(random.choices(string.digits, k=random.randint(2, 4)))
        return name + number

def generate_email(nickname):
    """根据昵称生成邮箱"""
    domains = ["university.edu", "school.edu.cn", "student.edu.cn"]
    # 移除非字母数字字符
    clean_name = ''.join(c for c in nickname if c.isalnum())
    username = clean_name.lower() + str(random.randint(1, 999))
    return f"{username}@{random.choice(domains)}"

def get_random_avatar():
    """获取随机头像"""
    # 方法1: 使用本地头像库
    avatar_files = list(AVATAR_DIR.glob("*.jpg"))
    if avatar_files:
        return random.choice(avatar_files)
    
    # 方法2: 使用在线头像API
    try:
        response = requests.get("https://i.pravatar.cc/300")
        if response.status_code == 200:
            img = Image.open(BytesIO(response.content))
            filename = f"avatar_{random.randint(1000, 9999)}.jpg"
            filepath = AVATAR_DIR / filename
            img.save(filepath)
            return filepath
    except Exception as e:
        print(f"获取头像失败: {e}")
    
    # 失败时返回默认头像
    return AVATAR_DIR / "default.jpg"

def create_account():
    """创建一个新账号并注册到目标系统"""
    nickname = generate_nickname()
    email = generate_email(nickname)
    password = "Test" + ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    
    # 获取头像并上传
    avatar_path = get_random_avatar()
    avatar_url = api_client.upload_file(avatar_path)
    
    # 注册账号
    account_data = {
        "email": email,
        "passwordHash": password,  # 前端会处理加密
        "nickname": nickname,
        "avatarUrl": avatar_url
    }
    
    # 发送验证码
    verify_code = api_client.send_verification_code(email)
    if not verify_code:
        print(f"发送验证码失败: {email}")
        return None
    
    # 注册账号
    response = api_client.register_account(account_data, email, verify_code)
    if not response or not response.get("success"):
        print(f"注册账号失败: {email}")
        return None
    
    # 保存账号信息到本地数据库
    account_id = response.get("data", {}).get("userId")
    db_account = {
        "account_id": account_id,
        "email": email,
        "password": password,
        "nickname": nickname,
        "avatar_url": avatar_url,
        "status": "active"
    }
    db.insert_account(db_account)
    
    print(f"成功创建账号: {nickname} ({email})")
    return db_account

def create_accounts(count=10):
    """批量创建账号"""
    accounts = []
    for _ in range(count):
        account = create_account()
        if account:
            accounts.append(account)
    return accounts
```

### 3.2 API客户端实现

```python
# api_client.py
import requests
import json
import time
import random
from pathlib import Path

from config import API_BASE_URL, TOKEN_EXPIRY_BUFFER

# 存储账号的token信息
token_cache = {}

def get_headers(token=None):
    """获取请求头"""
    headers = {
        "Content-Type": "application/json"
    }
    if token:
        headers["token"] = token
    return headers

def upload_file(file_path):
    """上传文件到目标系统"""
    url = f"{API_BASE_URL}/file/upload"
    
    try:
        with open(file_path, "rb") as f:
            files = {"file": (Path(file_path).name, f, "image/jpeg")}
            response = requests.post(url, files=files)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    return data.get("data", {}).get("url")
            
            print(f"上传文件失败: {response.text}")
    except Exception as e:
        print(f"上传文件异常: {e}")
    
    return None

def send_verification_code(email):
    """发送邮箱验证码"""
    url = f"{API_BASE_URL}/user/sendCode"
    data = {"email": email}
    
    try:
        response = requests.post(url, json=data, headers=get_headers())
        if response.status_code == 200:
            # 实际项目中验证码会发送到邮箱，这里模拟一个验证码
            # 在真实环境中，你需要一种方式获取验证码，可能需要与后端开发者协商一个测试模式
            return "123456"  # 模拟验证码
    except Exception as e:
        print(f"发送验证码异常: {e}")
    
    return None

def register_account(account_data, email, verify_code):
    """注册账号"""
    url = f"{API_BASE_URL}/user/register"
    
    verify_data = {
        "email": email,
        "code": verify_code
    }
    
    data = {
        "user": account_data,
        "verifyCodeRequest": verify_data
    }
    
    try:
        response = requests.post(url, json=data, headers=get_headers())
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        print(f"注册账号异常: {e}")
    
    return None

def login(email, password):
    """登录账号获取token"""
    url = f"{API_BASE_URL}/user/login"
    data = {
        "email": email,
        "passwordHash": password
    }
    
    try:
        response = requests.post(url, json=data, headers=get_headers())
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                token = result.get("data", {}).get("token")
                if token:
                    # 缓存token
                    token_cache[email] = {
                        "token": token,
                        "expires_at": time.time() + 3600 - TOKEN_EXPIRY_BUFFER  # 假设token有效期1小时
                    }
                    return token
    except Exception as e:
        print(f"登录异常: {e}")
    
    return None

def get_token(email, password):
    """获取有效的token，如果缓存中没有或已过期则重新登录"""
    cache_data = token_cache.get(email)
    
    if cache_data and time.time() < cache_data["expires_at"]:
        return cache_data["token"]
    
    # 重新登录获取token
    return login(email, password)

def create_post(account, content_text, images=None, tags=None):
    """发布动态"""
    email, password = account["email"], account["password"]
    token = get_token(email, password)
    
    if not token:
        print(f"获取token失败: {email}")
        return None
    
    url = f"{API_BASE_URL}/post/create"
    
    # 上传图片
    image_urls = []
    if images:
        for image_path in images:
            image_url = upload_file(image_path)
            if image_url:
                image_urls.append(image_url)
    
    data = {
        "content": content_text,
        "imageUrls": image_urls,
        "tags": tags or []
    }
    
    try:
        response = requests.post(url, json=data, headers=get_headers(token))
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                return result.get("data")
    except Exception as e:
        print(f"发布动态异常: {e}")
    
    return None

def like_post(account, post_id):
    """点赞动态"""
    email, password = account["email"], account["password"]
    token = get_token(email, password)
    
    if not token:
        print(f"获取token失败: {email}")
        return False
    
    url = f"{API_BASE_URL}/post/like/{post_id}"
    
    try:
        response = requests.post(url, headers=get_headers(token))
        if response.status_code == 200:
            result = response.json()
            return result.get("code") == 200
    except Exception as e:
        print(f"点赞动态异常: {e}")
    
    return False

def comment_post(account, post_id, comment_text):
    """评论动态"""
    email, password = account["email"], account["password"]
    token = get_token(email, password)
    
    if not token:
        print(f"获取token失败: {email}")
        return None
    
    url = f"{API_BASE_URL}/comment/create"
    
    data = {
        "postId": post_id,
        "content": comment_text
    }
    
    try:
        response = requests.post(url, json=data, headers=get_headers(token))
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                return result.get("data")
    except Exception as e:
        print(f"评论动态异常: {e}")
    
    return None

def get_posts(account, page=1, size=20):
    """获取动态列表"""
    email, password = account["email"], account["password"]
    token = get_token(email, password)
    
    if not token:
        print(f"获取token失败: {email}")
        return []
    
    url = f"{API_BASE_URL}/post/list?page={page}&size={size}"
    
    try:
        response = requests.get(url, headers=get_headers(token))
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                return result.get("data", {}).get("content", [])
    except Exception as e:
        print(f"获取动态列表异常: {e}")
    
    return []
```

### 3.3 内容生成

```python
# generators/content.py
import random
import os
from pathlib import Path
import openai
from PIL import Image

from config import OPENAI_API_KEY, IMAGES_DIR, CONTENT_TEMPLATES
import api_client
import db

# 设置OpenAI API密钥
openai.api_key = OPENAI_API_KEY

# 校园相关标签
CAMPUS_TAGS = [
    "学习", "考试", "图书馆", "实验室", "课程", "笔记", "作业",
    "社团", "活动", "比赛", "讲座", "志愿者", "校园风光",
    "食堂", "宿舍", "运动", "健身", "音乐", "电影", "游戏",
    "实习", "求职", "考研", "留学", "创业", "保研"
]

def get_random_images(count=1):
    """获取随机图片"""
    image_files = list(Path(IMAGES_DIR).glob("*.jpg"))
    if not image_files:
        return []
    
    selected = random.sample(image_files, min(count, len(image_files)))
    return [str(img) for img in selected]

def generate_content_with_openai(prompt):
    """使用OpenAI生成内容"""
    try:
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "你是一个大学生，正在校园社交平台上发布动态。请生成真实、自然的内容，不要太长，200字以内。"},
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.7
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"OpenAI生成内容失败: {e}")
        return None

def generate_content_from_template():
    """从模板生成内容"""
    template = random.choice(CONTENT_TEMPLATES)
    
    # 替换模板中的变量
    replacements = {
        "{地点}": random.choice(["图书馆", "教室", "实验室", "宿舍", "食堂", "操场", "社团活动室"]),
        "{课程}": random.choice(["高等数学", "大学物理", "数据结构", "计算机网络", "操作系统", "软件工程", "人工智能"]),
        "{时间}": random.choice(["今天", "昨天", "刚才", "这周", "这学期"]),
        "{心情}": random.choice(["开心", "充实", "疲惫", "兴奋", "紧张", "放松"]),
        "{活动}": random.choice(["社团活动", "志愿服务", "学生会工作", "班级聚会", "体育比赛", "文艺演出", "学术讲座"])
    }
    
    for key, value in replacements.items():
        template = template.replace(key, value)
    
    return template

def generate_content(use_openai=True):
    """生成动态内容"""
    if use_openai and OPENAI_API_KEY:
        # 随机选择一个主题
        topic = random.choice([
            "分享今天在校园的学习经历",
            "记录参加的一次校园活动",
            "描述校园里的一处风景",
            "分享一次考试或作业的心得",
            "记录和同学一起的时光",
            "分享一本最近在读的书",
            "记录在食堂或宿舍的日常"
        ])
        content = generate_content_with_openai(topic)
    else:
        content = generate_content_from_template()
    
    if not content:
        content = "今天在校园里度过了充实的一天，学习、运动、交友，大学生活真美好！"
    
    # 随机决定是否添加图片
    image_count = random.choices([0, 1, 2, 3], weights=[0.2, 0.4, 0.3, 0.1])[0]
    images = get_random_images(image_count) if image_count > 0 else []
    
    # 随机选择1-3个标签
    tag_count = random.randint(1, 3)
    tags = random.sample(CAMPUS_TAGS, tag_count)
    
    return {
        "content": content,
        "images": images,
        "tags": tags
    }

def create_post_for_account(account):
    """为指定账号创建一条动态"""
    content_data = generate_content()
    
    result = api_client.create_post(
        account,
        content_data["content"],
        content_data["images"],
        content_data["tags"]
    )
    
    if result:
        post_id = result.get("postId")
        # 保存到本地数据库
        post_data = {
            "post_id": post_id,
            "account_id": account["account_id"],
            "content": content_data["content"],
            "images": ",".join(content_data["images"]),
            "tags": ",".join(content_data["tags"])
        }
        db.insert_post(post_data)
        
        print(f"成功为 {account['nickname']} 创建动态: {post_id}")
        return post_data
    
    return None

def create_posts(count=20):
    """批量创建动态"""
    posts = []
    accounts = db.get_active_accounts()
    
    if not accounts:
        print("没有可用账号，请先创建账号")
        return []
    
    for _ in range(count):
        account = random.choice(accounts)
        post = create_post_for_account(account)
        if post:
            posts.append(post)
            # 随机暂停一段时间，避免请求过于频繁
            time.sleep(random.uniform(1, 3))
    
    return posts
```

### 3.4 行为模拟

```python
# generators/behavior.py
import random
import time
from datetime import datetime, timedelta

import api_client
import db

def generate_comment():
    """生成随机评论内容"""
    comments = [
        "很棒的分享！",
        "支持一下~",
        "学习了，谢谢分享",
        "这个观点很有意思",
        "我也有类似的经历",
        "请问可以详细说说吗？",
        "图片拍得真好看",
        "这个地方我也去过，很赞",
        "加油，看好你哦",
        "这个内容对我很有帮助",
        "期待你的下一条动态",
        "我们下次可以一起去",
        "谢谢分享，收藏了",
        "这个活动我也参加了",
        "你的观点很独到"
    ]
    return random.choice(comments)

def simulate_browse(account, count=10):
    """模拟浏览行为"""
    posts = api_client.get_posts(account)
    
    if not posts:
        print(f"获取动态列表失败: {account['email']}")
        return []
    
    browsed = []
    for _ in range(min(count, len(posts))):
        post = random.choice(posts)
        post_id = post.get("postId")
        
        # 记录浏览行为
        browse_data = {
            "account_id": account["account_id"],
            "post_id": post_id,
            "behavior_type": "browse",
            "created_at": datetime.now().isoformat()
        }
        db.insert_behavior(browse_data)
        
        browsed.append(post_id)
        print(f"{account['nickname']} 浏览了动态: {post_id}")
        
        # 随机暂停
        time.sleep(random.uniform(0.5, 2))
    
    return browsed

def simulate_like(account, count=5):
    """模拟点赞行为"""
    # 先获取已浏览的动态
    browsed_posts = db.get_browsed_posts(account["account_id"])
    
    if not browsed_posts:
        # 如果没有浏览记录，先模拟浏览
        browsed_posts = simulate_browse(account)
    
    if not browsed_posts:
        print(f"没有可点赞的动态: {account['email']}")
        return []
    
    # 随机选择一些动态点赞
    to_like = random.sample(browsed_posts, min(count, len(browsed_posts)))
    liked = []
    
    for post_id in to_like:
        success = api_client.like_post(account, post_id)
        
        if success:
            # 记录点赞行为
            like_data = {
                "account_id": account["account_id"],
                "post_id": post_id,
                "behavior_type": "like",
                "created_at": datetime.now().isoformat()
            }
            db.insert_behavior(like_data)
            
            liked.append(post_id)
            print(f"{account['nickname']} 点赞了动态: {post_id}")
        
        # 随机暂停
        time.sleep(random.uniform(0.5, 1.5))
    
    return liked

def simulate_comment(account, count=3):
    """模拟评论行为"""
    # 先获取已浏览的动态
    browsed_posts = db.get_browsed_posts(account["account_id"])
    
    if not browsed_posts:
        # 如果没有浏览记录，先模拟浏览
        browsed_posts = simulate_browse(account)
    
    if not browsed_posts:
        print(f"没有可评论的动态: {account['email']}")
        return []
    
    # 随机选择一些动态评论
    to_comment = random.sample(browsed_posts, min(count, len(browsed_posts)))
    commented = []
    
    for post_id in to_comment:
        comment_text = generate_comment()
        result = api_client.comment_post(account, post_id, comment_text)
        
        if result:
            comment_id = result.get("commentId")
            # 记录评论行为
            comment_data = {
                "account_id": account["account_id"],
                "post_id": post_id,
                "comment_id": comment_id,
                "content": comment_text,
                "behavior_type": "comment",
                "created_at": datetime.now().isoformat()
            }
            db.insert_behavior(comment_data)
            
            commented.append(post_id)
            print(f"{account['nickname']} 评论了动态: {post_id}")
        
        # 随机暂停
        time.sleep(random.uniform(1, 2))
    
    return commented

def simulate_behaviors(account_count=5, browse_count=10, like_count=5, comment_count=3):
    """模拟用户行为"""
    accounts = db.get_active_accounts()
    
    if not accounts:
        print("没有可用账号，请先创建账号")
        return
    
    # 随机选择一些账号进行行为模拟
    selected_accounts = random.sample(accounts, min(account_count, len(accounts)))
    
    for account in selected_accounts:
        print(f"模拟 {account['nickname']} 的行为...")
        
        # 模拟浏览
        simulate_browse(account, browse_count)
        
        # 模拟点赞
        simulate_like(account, like_count)
        
        # 模拟评论
        simulate_comment(account, comment_count)
        
        print(f"{account['nickname']} 的行为模拟完成")
        
        # 账号之间随机暂停
        time.sleep(random.uniform(2, 5))
```

### 3.5 数据库操作

```python
# db.py
import sqlite3
import json
from pathlib import Path
from datetime import datetime

from config import DB_PATH

def init_db():
    """初始化数据库"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 创建账号表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id TEXT UNIQUE,
        email TEXT UNIQUE,
        password TEXT,
        nickname TEXT,
        avatar_url TEXT,
        status TEXT DEFAULT 'active',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # 创建动态表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS posts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        post_id TEXT UNIQUE,
        account_id TEXT,
        content TEXT,
        images TEXT,
        tags TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES accounts (account_id)
    )
    ''')
    
    # 创建行为表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS behaviors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id TEXT,
        post_id TEXT,
        comment_id TEXT,
        content TEXT,
        behavior_type TEXT,
        created_at TEXT,
        FOREIGN KEY (account_id) REFERENCES accounts (account_id),
        FOREIGN KEY (post_id) REFERENCES posts (post_id)
    )
    ''')
    
    conn.commit()
    conn.close()

def insert_account(account):
    """插入账号数据"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
    INSERT INTO accounts (account_id, email, password, nickname, avatar_url, status)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        account["account_id"],
        account["email"],
        account["password"],
        account["nickname"],
        account["avatar_url"],
        account["status"]
    ))
    
    conn.commit()
    conn.close()

def insert_post(post):
    """插入动态数据"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
    INSERT INTO posts (post_id, account_id, content, images, tags)
    VALUES (?, ?, ?, ?, ?)
    ''', (
        post["post_id"],
        post["account_id"],
        post["content"],
        post["images"],
        post["tags"]
    ))
    
    conn.commit()
    conn.close()

def insert_behavior(behavior):
    """插入行为数据"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
    INSERT INTO behaviors (account_id, post_id, comment_id, content, behavior_type, created_at)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        behavior["account_id"],
        behavior.get("post_id"),
        behavior.get("comment_id"),
        behavior.get("content"),
        behavior["behavior_type"],
        behavior["created_at"]
    ))
    
    conn.commit()
    conn.close()

def get_active_accounts():
    """获取活跃账号列表"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    cursor.execute('''
    SELECT * FROM accounts WHERE status = 'active'
    ''')
    
    accounts = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return accounts

def get_browsed_posts(account_id):
    """获取账号已浏览的动态ID列表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
    SELECT DISTINCT post_id FROM behaviors 
    WHERE account_id = ? AND behavior_type = 'browse'
    ''', (account_id,))
    
    post_ids = [row[0] for row in cursor.fetchall()]
    conn.close()
    
    return post_ids

def get_statistics():
    """获取