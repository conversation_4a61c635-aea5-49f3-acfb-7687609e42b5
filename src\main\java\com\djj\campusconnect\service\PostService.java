package com.djj.campusconnect.service;

import com.djj.campusconnect.pojo.Post;

import java.util.List;

public interface PostService {


    public Post getPostById(Integer postId);

    public List<Post> getAllPost();

    public Boolean verifyPostOfCurrentUser(Integer postId);
    
    public List<Integer> getHotPost(Integer start, Integer postNum);

    public List<Integer> getNewPost(Integer start, Integer postNum);

    public Integer addPost(Post post);
}
