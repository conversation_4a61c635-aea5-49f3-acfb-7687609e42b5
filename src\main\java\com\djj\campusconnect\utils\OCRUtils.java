package com.djj.campusconnect.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.io.IOException;
import java.net.URL;
import java.net.HttpURLConnection;
import java.util.HashMap;
import java.util.Map;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

@Component
public class OCRUtils {

    private static final String GLM4V_API_URL = "https://open.bigmodel.cn/api/paas/v4/chat/completions";
    @Value("${glm4v.api.key}")
    private static String API_KEY;
    
    /**
     * 使用GLM-4V-Flash API进行图像识别
     * @param imageUrl 图片URL
     * @param type 识别类型：0-识别学校名称，1-识别院系名称
     * @return 识别结果
     * @throws Exception 如果处理过程中发生错误
     */
    public String performOCR(String imageUrl, int type) throws Exception {
        // 根据type选择提示词
        String prompt;
        if (type == 0) {
            prompt = "仅告知我图像中的学校名称";
        } else if (type == 1) {
            prompt = "仅告知我图像中的院系名称";
        } else {
            throw new IllegalArgumentException("Type must be 0 or 1");
        }
        
        // 构建API请求
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "glm-4v-flash"); // 使用免费版本
        requestBody.put("stream", false); // 非流式输出
        
        // 构建消息数组
        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", buildContent(imageUrl, prompt));
        
        // 将消息放入数组
        Object[] messages = new Object[]{message};
        requestBody.put("messages", messages);
        
        // 发送请求并获取响应
        String response = sendRequest(GLM4V_API_URL, JSON.toJSONString(requestBody));
        
        // 解析响应
        JSONObject jsonResponse = JSON.parseObject(response);
        
        // 打印解析前的完整响应以便调试
        System.out.println("Full response JSON: " + jsonResponse.toJSONString());
        
        // 检查是否有错误信息
        if (jsonResponse.containsKey("error")) {
            JSONObject error = jsonResponse.getJSONObject("error");
            throw new RuntimeException("API Error: " + error.getString("message"));
        }
        
        // 正常解析响应
        JSONObject choice = jsonResponse.getJSONArray("choices").getJSONObject(0);
        JSONObject messageGet = choice.getJSONObject("message");
        String content = messageGet.getString("content");
        
        return content;
    }

    /**
     * 构建GLM-4V-Flash API请求内容
     * @param imageUrl 图片URL
     * @param prompt 提示词
     * @return API请求内容
     */
    private Object[] buildContent(String imageUrl, String prompt) {
        // 按照API文档要求构建内容数组
        // 先创建图片URL内容
        Map<String, Object> imageContent = new HashMap<>();
        imageContent.put("type", "image_url");
        
        Map<String, String> imageUrlMap = new HashMap<>();
        imageUrlMap.put("url", imageUrl);
        imageContent.put("image_url", imageUrlMap);
        
        // 创建文本内容
        Map<String, Object> textContent = new HashMap<>();
        textContent.put("type", "text");
        textContent.put("text", prompt);
        
        // 返回内容数组，顺序是图片在前，文本在后
        return new Object[]{imageContent, textContent};
    }
    
    /**
     * 发送HTTP请求到GLM-4V-Flash API
     * @param apiUrl API URL
     * @param requestBody 请求体JSON字符串
     * @return API响应
     * @throws IOException 如果发送请求过程中发生错误
     */
    private String sendRequest(String apiUrl, String requestBody) throws IOException {
        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Authorization", "Bearer " + API_KEY);
        connection.setDoOutput(true);
        
        // 打印请求信息用于调试
        System.out.println("Request URL: " + apiUrl);
        System.out.println("Request Headers: " + connection.getRequestProperties());
        System.out.println("Request Body: " + requestBody);
        
        // 发送请求
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        // 检查响应码
        int responseCode = connection.getResponseCode();
        System.out.println("Response Code: " + responseCode);
        
        // 读取响应
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(
                    responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream(), 
                    StandardCharsets.UTF_8))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine);
            }
        }
        
        // 打印响应内容用于调试
        System.out.println("Response: " + response.toString());
        
        return response.toString();
    }

}
