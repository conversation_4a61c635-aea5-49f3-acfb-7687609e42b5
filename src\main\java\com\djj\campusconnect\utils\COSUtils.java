package com.djj.campusconnect.utils;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.region.Region;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

/**
 * COSUtils
 */
@Component
public class COSUtils {
    private final COSClient cosClient;
    private final String bucketName;
    private final String baseURL;

    public COSUtils(
            @Value("${qcloud.cos.bucketName}") String bucketName,
            @Value("${qcloud.cos.baseURL}") String baseURL,
            @Value("${qcloud.cos.secretId}") String secretId,
            @Value("${qcloud.cos.secretKey}") String secretKey) {

        this.bucketName = bucketName;
        this.baseURL = baseURL;

        // 1 初始化用户身份信息（secretId, secretKey）。
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);

        // 2 设置 bucket 的地域
        Region region = new Region("ap-guangzhou");
        ClientConfig clientConfig = new ClientConfig(region);
        clientConfig.setHttpProtocol(HttpProtocol.https);

        // 3 生成 cos 客户端。
        this.cosClient = new COSClient(cred, clientConfig);
    }

    public String uploadImage(MultipartFile file) throws IOException {
        String originalFileName = file.getOriginalFilename();
        String suffixName = originalFileName.substring(originalFileName.lastIndexOf("."));
        String key = UUID.randomUUID() + suffixName;
        String fileURL = baseURL + "image/" + key;
        InputStream inputStream = file.getInputStream();
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(file.getSize());
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, inputStream, objectMetadata);
        cosClient.putObject(putObjectRequest);
        return fileURL;
    }

    public String uploadVideo(MultipartFile file) throws IOException {
        String originalFileName = file.getOriginalFilename();
        String suffixName = originalFileName.substring(originalFileName.lastIndexOf("."));
        String key = UUID.randomUUID() + suffixName;
        String fileURL = baseURL + "video/" + key;
        InputStream inputStream = file.getInputStream();
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(file.getSize());
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, inputStream, objectMetadata);
        cosClient.putObject(putObjectRequest);
        return fileURL;
    }
}
